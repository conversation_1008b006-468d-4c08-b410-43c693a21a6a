import numpy as np
import pickle
import torch
import os
import scipy.io as sio
import h5py
from torch.utils.data import Dataset, DataLoader
from sklearn.model_selection import train_test_split


class RML2016Dataset(Dataset):
    """
    RML2016.10a数据集的PyTorch数据集类
    
    该数据集包含不同调制类型和SNR级别的无线电信号I/Q样本
    """
    def __init__(self, X, y, snrs=None, device='cpu'):
        """
        初始化数据集
        
        参数:
            X: 信号数据，形状为 [n_samples, 2, signal_length]
            y: 调制类型标签，形状为 [n_samples]
            snrs: SNR值，形状为 [n_samples]，可选
            device: 将数据放在哪个设备上('cpu'或'cuda')
        """
        self.X = torch.tensor(X, dtype=torch.float32, device=device)
        self.y = torch.tensor(y, dtype=torch.long, device=device)
        if snrs is not None:
            self.snrs = torch.tensor(snrs, dtype=torch.float32, device=device)
        else:
            self.snrs = None
            
    def __len__(self):
        """
        返回数据集大小
        """
        return len(self.X)
    
    def __getitem__(self, idx):
        """
        获取单个样本
        
        参数:
            idx: 样本索引
            
        返回:
            (X, y, snr): 信号样本，标签和SNR值的元组
        """
        if self.snrs is not None:
            return self.X[idx], self.y[idx], self.snrs[idx]
        else:
            return self.X[idx], self.y[idx], torch.tensor(0.0)


class RML201801aDataset(Dataset):
    """
    RML2018.01a数据集的PyTorch数据集类

    该数据集包含24种调制类型和多个SNR级别的无线电信号I/Q样本
    数据存储在HDF5格式中，形状为32x32x2
    """
    def __init__(self, X, y, snrs=None, device='cpu'):
        """
        初始化数据集

        参数:
            X: 信号数据，形状为 [n_samples, 2, signal_length]
            y: 调制类型标签，形状为 [n_samples]
            snrs: SNR值，形状为 [n_samples]，可选
            device: 将数据放在哪个设备上('cpu'或'cuda')
        """
        self.X = torch.tensor(X, dtype=torch.float32, device=device)
        self.y = torch.tensor(y, dtype=torch.long, device=device)
        if snrs is not None:
            self.snrs = torch.tensor(snrs, dtype=torch.float32, device=device)
        else:
            self.snrs = None

    def __len__(self):
        """
        返回数据集大小
        """
        return len(self.X)

    def __getitem__(self, idx):
        """
        获取单个样本

        参数:
            idx: 样本索引

        返回:
            (X, y, snr): 信号样本，标签和SNR值的元组
        """
        if self.snrs is not None:
            return self.X[idx], self.y[idx], self.snrs[idx]
        else:
            return self.X[idx], self.y[idx], torch.tensor(0.0)


class HisarModDataset(Dataset):
    def __init__(self, data_path, labels_path, snr_path, sequence_length=1024, is_training=True, device='cpu'):
        """
        初始化HisarMod数据集
        Args:
            data_path: 数据文件路径 (.mat 格式)
            labels_path: 标签文件路径 (.csv 格式)
            snr_path: SNR文件路径 (.csv 格式)
            sequence_length: 序列长度，如果与原始数据不同，将进行裁剪或填充
            is_training: 是否为训练模式
            device: 将数据放在哪个设备上('cpu'或'cuda')
        """
        self.sequence_length = sequence_length
        self.is_training = is_training
        self.data_path = data_path
        self.device = device
        
        # 提示正在加载数据集
        print(f"加载数据集: {os.path.basename(data_path)}")
        
        # 加载标签和SNR
        raw_labels = np.loadtxt(labels_path, delimiter=',')
        raw_snrs = np.loadtxt(snr_path, delimiter=',')

        # 检查标签范围并进行映射
        unique_labels = np.unique(raw_labels)
        print(f"原始标签范围: {unique_labels.min()} 到 {unique_labels.max()}")
        print(f"原始标签值: {sorted(unique_labels)}")

        # 创建标签映射，将原始标签映射到连续的0-N范围
        label_mapping = {old_label: new_label for new_label, old_label in enumerate(sorted(unique_labels))}
        mapped_labels = np.array([label_mapping[label] for label in raw_labels])

        self.labels = torch.from_numpy(mapped_labels).long()
        self.snrs = torch.from_numpy(raw_snrs).float()

        print(f"标签数量: {len(self.labels)}")
        print(f"SNR数量: {len(self.snrs)}")
        print(f"类别数: {len(torch.unique(self.labels))}")
        print(f"映射后标签范围: {self.labels.min().item()} 到 {self.labels.max().item()}")
        print(f"SNR范围: {self.snrs.min():.1f} 到 {self.snrs.max():.1f} dB")
        
        # 加载数据
        self._load_data(data_path)
        
        # 确保数据、标签和SNR的数量一致
        assert len(self.data) == len(self.labels) == len(self.snrs), \
            f"数据、标签和SNR数量不一致: {len(self.data)}, {len(self.labels)}, {len(self.snrs)}"
        
        print(f"数据集加载完成: {len(self.data)}样本, {len(torch.unique(self.labels))}类, {len(torch.unique(self.snrs))}种SNR值")
    
    def _load_data(self, data_path):
        """加载数据文件"""
        if not os.path.exists(data_path):
            raise FileNotFoundError(f"文件不存在: {data_path}")

        success = False
        error_messages = []

        # 方法1: 尝试先用scipy.io获取文件信息
        try:
            mat_info = sio.whosmat(data_path)

            # 确定使用哪种方法加载
            file_size = os.path.getsize(data_path) / (1024 * 1024)  # 大小(MB)

            # 如果文件较小，直接使用scipy.io加载
            if file_size < 1000:  # 小于1GB
                self._load_with_scipy(data_path, mat_info)
                success = True
            else:
                # 对于大文件，使用部分加载
                try:
                    import h5py
                    self._load_with_h5py(data_path)
                    success = True
                except ImportError:
                    print("警告: h5py未安装，无法部分加载大文件。将尝试完整加载...")
                    self._load_with_scipy(data_path, mat_info)
                    success = True
                except Exception as e:
                    print(f"使用h5py加载失败: {e}，将尝试使用scipy.io")
                    self._load_with_scipy(data_path, mat_info)
                    success = True
        except Exception as e:
            error_messages.append(f"scipy.io方法失败: {str(e)}")
            print(f"检查MAT文件信息时出错: {e}")

            # 方法2: 尝试直接使用scipy.io加载
            try:
                self._load_with_scipy(data_path)
                success = True
            except Exception as e2:
                error_messages.append(f"scipy.io直接加载失败: {str(e2)}")
                print(f"使用scipy.io加载失败: {e2}")

                # 方法3: 尝试使用h5py加载（HDF5格式的MAT文件）
                try:
                    import h5py
                    self._load_with_h5py_direct(data_path)
                    success = True
                    print("使用h5py成功加载HDF5格式的MAT文件")
                except ImportError:
                    error_messages.append("h5py模块未安装，无法加载HDF5格式的MAT文件")
                except Exception as e3:
                    error_messages.append(f"h5py加载失败: {str(e3)}")
                    print(f"使用h5py加载失败: {e3}")

        if not success:
            error_msg = "无法加载MAT文件，尝试了以下方法:\n"
            for i, msg in enumerate(error_messages):
                error_msg += f"方法{i+1}: {msg}\n"
            error_msg += "\n请检查文件格式或尝试转换为NPY格式。"
            raise ValueError(error_msg)
    
    def _load_with_scipy(self, data_path, mat_info=None):
        """使用scipy.io加载MAT文件"""
        print(f"尝试加载MAT文件: {data_path}")
        
        try:
            mat_data = sio.loadmat(data_path)
            
            # 查找数据变量
            data_vars = [k for k in mat_data.keys() if not k.startswith('__')]
            
            if not data_vars:
                raise ValueError("MAT文件中没有找到数据变量")
            
            # 使用第一个数据变量
            data_var = data_vars[0]
            data = mat_data[data_var]
            
            print(f"加载的数据形状: {data.shape}")
            print(f"使用scipy.io成功加载MAT文件，数据变量: {data_var}")
            
            # 处理数据形状
            self._process_data_shape(data)
            
        except Exception as e:
            print(f"使用scipy.io加载失败: {e}")
            raise
    
    def _load_with_h5py(self, data_path):
        """使用h5py加载大型MAT文件"""
        print(f"尝试使用h5py加载大型MAT文件: {data_path}")

        try:
            import h5py

            with h5py.File(data_path, 'r') as f:
                # 查找数据变量
                data_vars = [k for k in f.keys() if not k.startswith('#')]

                if not data_vars:
                    raise ValueError("MAT文件中没有找到数据变量")

                # 使用第一个数据变量
                data_var = data_vars[0]
                self.data_ref = f[data_var]

                print(f"加载的数据形状: {self.data_ref.shape}")
                print(f"使用h5py成功加载MAT文件，数据变量: {data_var}")

                # 获取数据形状
                self.data_shape = self.data_ref.shape

                # 确定数据布局
                if len(self.data_shape) == 3:
                    if self.data_shape[1] == 2 and self.data_shape[0] > 10:
                        # 形状为 (样本数, 2, 序列长度)
                        self.transpose_needed = False
                        self.num_samples = self.data_shape[0]
                        self.num_channels = self.data_shape[1]
                        self.data_length = self.data_shape[2]
                    elif self.data_shape[1] == 2 and self.data_shape[2] > 10:
                        # 形状为 (序列长度, 2, 样本数) - 需要转置
                        self.transpose_needed = True
                        self.num_samples = self.data_shape[2]
                        self.num_channels = self.data_shape[1]
                        self.data_length = self.data_shape[0]
                    else:
                        raise ValueError(f"数据维度不符合要求: {self.data_shape}")
                else:
                    raise ValueError(f"不支持的数据形状: {self.data_shape}")

                # 标记数据加载模式
                self.use_h5py = True

        except Exception as e:
            print(f"使用h5py加载失败: {e}")
            raise

    def _load_with_h5py_direct(self, data_path):
        """直接使用h5py加载HDF5格式的MAT文件"""
        print(f"尝试使用h5py直接加载HDF5格式的MAT文件: {data_path}")

        try:
            import h5py

            with h5py.File(data_path, 'r') as f:
                # 查找可能的数据变量
                data_vars = list(f.keys())
                print(f"文件中的变量: {data_vars}")

                if not data_vars:
                    raise ValueError("HDF5文件中没有找到数据变量")

                # 尝试找到数据变量
                data_var = None
                for var in data_vars:
                    if not var.startswith('#') and not var.startswith('__'):
                        data_var = var
                        break

                if data_var is None:
                    data_var = data_vars[0]  # 使用第一个变量

                # 加载数据
                data = np.array(f[data_var])
                print(f"使用h5py成功加载MAT文件，数据变量: {data_var}")
                print(f"加载的数据形状: {data.shape}")

                # 处理数据形状
                self._process_data_shape(data)

        except Exception as e:
            print(f"使用h5py直接加载失败: {e}")
            raise
    
    def _process_data_shape(self, data):
        """处理数据形状，确保格式正确"""
        print(f"原始数据形状: {data.shape}")
        
        # 确保数据是3维的: (样本数, 通道数, 序列长度)
        if len(data.shape) == 3:
            if data.shape[1] == 2:
                # 形状已经正确: (样本数, 2, 序列长度)
                processed_data = data
            elif data.shape[2] == 2:
                # 需要转置: (样本数, 序列长度, 2) -> (样本数, 2, 序列长度)
                processed_data = data.transpose(0, 2, 1)
            else:
                raise ValueError(f"无法识别的数据形状: {data.shape}")
        else:
            raise ValueError(f"不支持的数据维度: {data.shape}")
        
        # 使用数据的原始长度，不进行截断或填充 - 让模型自适应
        current_length = processed_data.shape[2]
        self.actual_sequence_length = current_length
        print(f"使用数据原始长度: {current_length}")

        # 转换为float32并存储
        self.data = processed_data.astype(np.float32)
        print(f"处理后数据形状: {self.data.shape}")
    
    def __len__(self):
        """返回数据集大小"""
        if hasattr(self, 'use_h5py') and self.use_h5py:
            return self.num_samples
        else:
            return len(self.data)
    
    def __getitem__(self, idx):
        """获取单个样本"""
        try:
            if hasattr(self, 'use_h5py') and self.use_h5py:
                # 使用h5py按需加载
                if hasattr(self, 'transpose_needed') and self.transpose_needed:
                    # 形状 (序列长度, 2, 样本数) -> (2, 序列长度)
                    signal_data = self.data_ref[:, :, idx].T
                else:
                    # 形状 (样本数, 2, 序列长度) -> (2, 序列长度)
                    signal_data = self.data_ref[idx]
                
                # 转换为张量
                signal = torch.from_numpy(signal_data).float()
            else:
                # 使用预加载的数据
                signal_data = self.data[idx]
                signal = torch.from_numpy(signal_data).float()
            
            # 归一化信号
            mean = signal.mean()
            std = signal.std() + 1e-8
            signal = (signal - mean) / std
            
            # 将数据移到指定设备
            signal = signal.to(self.device)
            return signal, self.labels[idx].to(self.device), self.snrs[idx].to(self.device)
            
        except Exception as e:
            print(f"加载样本 {idx} 时出错: {e}")
            # 返回零张量作为备用
            signal = torch.zeros(2, self.sequence_length, device=self.device)
            return signal, self.labels[idx].to(self.device), self.snrs[idx].to(self.device)


def load_rml_dataset(file_path, modulations, samples_per_key=None):
    """
    加载RML2016.10a数据集

    参数:
        file_path: 数据集文件路径(.pkl格式)
        modulations: 要使用的调制类型列表，例如 ['8PSK', 'AM-DSB', ...]
        samples_per_key: 每个(调制类型,SNR)对的样本数量，None表示使用所有样本

    返回:
        X: 形状为 [n_samples, 2, signal_length] 的信号数据
        labels: 形状为 [n_samples] 的标签
        snrs: 形状为 [n_samples] 的SNR值
    """
    print(f"从 {file_path} 加载数据集...")
    try:
        Xd = pickle.load(open(file_path, 'rb'), encoding='latin')
    except Exception as e:
        print(f"加载数据集时出错: {e}")
        raise

    # 创建调制类型映射
    modulation_map = {mod: idx for idx, mod in enumerate(modulations)}
    print(f"调制类型映射: {modulation_map}")

    # 提取唯一SNR值和调制类型
    snrs, mods = map(lambda j: sorted(list(set(map(lambda x: x[j], Xd.keys())))), [1, 0])
    print(f"SNR值范围: {min(snrs)} dB 到 {max(snrs)} dB")
    print(f"可用调制类型: {mods}")

    # 收集数据
    X, labels, snr_values = [], [], []

    for mod in mods:
        if mod not in modulations:
            print(f"跳过未请求的调制类型 {mod}")
            continue

        mod_samples = 0
        for snr in snrs:
            n_samples = Xd[(mod, snr)].shape[0]
            if samples_per_key:
                n_samples = min(n_samples, int(samples_per_key))

            for i in range(n_samples):
                X.append(Xd[(mod, snr)][i])
                labels.append(modulation_map[mod])
                snr_values.append(snr)

            mod_samples += n_samples

        print(f"调制类型 {mod} 加载了 {mod_samples} 个样本")

    X = np.array(X)
    labels = np.array(labels)
    snr_values = np.array(snr_values)

    print(f"数据集加载完成，共 {len(X)} 个样本，信号长度 {X.shape[-1]}")
    return X, labels, snr_values


def split_dataset(X, labels, snrs, train_ratio=0.7, seed=42, stratify_by_snr=False):
    """
    划分训练集和测试集

    参数:
        X: 信号数据
        labels: 标签
        snrs: SNR值
        train_ratio: 训练集比例
        seed: 随机种子
        stratify_by_snr: 是否按SNR分层采样，确保训练和测试集有相似的SNR分布

    返回:
        (X_train, y_train, snr_train), (X_test, y_test, snr_test): 训练集和测试集
    """
    np.random.seed(seed)
    n_samples = len(X)

    if stratify_by_snr:
        # 按SNR和调制类型分层
        unique_snrs = np.unique(snrs)
        unique_labels = np.unique(labels)

        train_indices = []
        test_indices = []

        for snr in unique_snrs:
            for label in unique_labels:
                mask = (snrs == snr) & (labels == label)
                indices = np.where(mask)[0]
                np.random.shuffle(indices)

                split_idx = int(len(indices) * train_ratio)
                train_indices.extend(indices[:split_idx])
                test_indices.extend(indices[split_idx:])
    else:
        # 随机分割
        indices = np.random.permutation(n_samples)
        train_size = int(train_ratio * n_samples)

        train_indices = indices[:train_size]
        test_indices = indices[train_size:]

    X_train = X[train_indices]
    y_train = labels[train_indices]
    snr_train = snrs[train_indices]

    X_test = X[test_indices]
    y_test = labels[test_indices]
    snr_test = snrs[test_indices]

    print(f"训练集: {len(X_train)} 样本")
    print(f"测试集: {len(X_test)} 样本")

    return (X_train, y_train, snr_train), (X_test, y_test, snr_test)


def load_rml201801a_dataset(file_path, modulations=None, use_all_snr=True, train_test_val_split=(0.7, 0.15, 0.15)):
    """
    加载RML2018.01a数据集

    参数:
        file_path: HDF5数据集文件路径
        modulations: 要使用的调制类型列表，None表示使用所有调制类型
        use_all_snr: 是否使用所有SNR级别的数据，True表示使用全部数据
        train_test_val_split: 训练/测试/验证集划分比例，元组格式(train, test, val)

    返回:
        (X_train, y_train, snr_train), (X_test, y_test, snr_test), (X_val, y_val, snr_val): 训练、测试、验证集
    """
    print(f"从 {file_path} 加载RML2018.01a数据集...")

    # RML2018.01a数据集的24种调制类型
    base_modulation_classes = [
        'OOK', '4ASK', '8ASK', 'BPSK', 'QPSK', '8PSK', '16PSK', '32PSK',
        '16APSK', '32APSK', '64APSK', '128APSK', '16QAM', '32QAM', '64QAM',
        '128QAM', '256QAM', 'AM-SSB-WC', 'AM-SSB-SC', 'AM-DSB-WC', 'AM-DSB-SC',
        'FM', 'GMSK', 'OQPSK'
    ]

    # 如果没有指定调制类型，使用所有类型
    if modulations is None:
        selected_modulation_classes = base_modulation_classes
    else:
        selected_modulation_classes = modulations

    # 创建调制类型到索引的映射
    modulation_to_idx = {mod: idx for idx, mod in enumerate(selected_modulation_classes)}

    print(f"选择的调制类型 ({len(selected_modulation_classes)}): {selected_modulation_classes}")

    # 获取选择的调制类型在原始数据集中的索引
    selected_classes_id = [base_modulation_classes.index(cls) for cls in selected_modulation_classes]

    try:
        # 打开HDF5文件
        dataset_file = h5py.File(file_path, "r")

        # 检查数据集结构
        print(f"数据集键: {list(dataset_file.keys())}")

        # 获取数据和标签
        X_data = None
        y_data = None

        if use_all_snr:
            # 使用所有SNR级别的数据
            print("使用所有SNR级别的数据...")

            for id in selected_classes_id:
                # 每个调制类型有106496个样本，包含26个SNR级别，每个SNR级别4096个样本
                # 提取该调制类型的所有数据
                start_idx = 106496 * id
                end_idx = 106496 * (id + 1)

                X_slice = dataset_file['X'][start_idx:end_idx]
                y_slice = dataset_file['Y'][start_idx:end_idx]

                if X_data is not None:
                    X_data = np.concatenate([X_data, X_slice], axis=0)
                    y_data = np.concatenate([y_data, y_slice], axis=0)
                else:
                    X_data = X_slice
                    y_data = y_slice
        else:
            # 使用部分SNR级别的数据（与原始代码示例相同，使用最后4个SNR级别）
            print("使用部分SNR级别的数据（最后4个SNR级别）...")
            N_SNR = 4  # 从30 SNR到22 SNR

            for id in selected_classes_id:
                X_slice = dataset_file['X'][(106496*(id+1) - 4096*N_SNR):106496*(id+1)]
                y_slice = dataset_file['Y'][(106496*(id+1) - 4096*N_SNR):106496*(id+1)]

                if X_data is not None:
                    X_data = np.concatenate([X_data, X_slice], axis=0)
                    y_data = np.concatenate([y_data, y_slice], axis=0)
                else:
                    X_data = X_slice
                    y_data = y_slice

        # 关闭文件
        dataset_file.close()

        print(f"原始数据形状: X={X_data.shape}, Y={y_data.shape}")

        # 重新整形数据从32x32x2到2x1024格式以适应MAMC
        # 原始数据是32x32x2，我们需要将其转换为2x1024
        X_data = X_data.reshape(len(X_data), 32, 32, 2)

        # 将32x32x2重新整形为2x1024
        # 方法1：将32x32展平为1024，然后转置通道维度
        X_reshaped = np.zeros((len(X_data), 2, 1024), dtype=np.float32)
        for i in range(len(X_data)):
            # I通道 (第0个通道)
            X_reshaped[i, 0, :] = X_data[i, :, :, 0].flatten()
            # Q通道 (第1个通道)
            X_reshaped[i, 1, :] = X_data[i, :, :, 1].flatten()

        X_data = X_reshaped

        print(f"重新整形后数据形状: {X_data.shape}")

        # 处理标签数据
        # y_data是one-hot编码，需要转换为类别索引
        if len(y_data.shape) > 1 and y_data.shape[1] > 1:
            # 如果是one-hot编码，转换为类别索引
            y_labels = np.argmax(y_data, axis=1)
        else:
            y_labels = y_data.flatten()

        # 重新映射标签到连续的索引
        y_mapped = np.zeros_like(y_labels)
        for i, label in enumerate(y_labels):
            # 找到对应的调制类型
            if label < len(base_modulation_classes):
                mod_name = base_modulation_classes[label]
                if mod_name in modulation_to_idx:
                    y_mapped[i] = modulation_to_idx[mod_name]
                else:
                    print(f"警告: 调制类型 {mod_name} 不在选择列表中")

        # 生成SNR值（假设数据按SNR顺序排列）
        # RML2018.01a包含26个SNR级别，从-20dB到30dB，步长2dB
        if use_all_snr:
            snr_levels = np.arange(-20, 32, 2)  # -20到30，步长2
            samples_per_snr = 4096
        else:
            snr_levels = np.arange(22, 32, 2)  # 最后4个SNR级别：22, 24, 26, 28, 30
            samples_per_snr = 4096

        # 为每个样本分配SNR值
        snr_values = []
        for mod_idx in range(len(selected_modulation_classes)):
            for snr in snr_levels:
                snr_values.extend([snr] * samples_per_snr)

        snr_values = np.array(snr_values[:len(X_data)])  # 确保长度匹配

        print(f"数据集加载完成:")
        print(f"  样本数量: {len(X_data)}")
        print(f"  调制类型数量: {len(selected_modulation_classes)}")
        print(f"  SNR级别: {snr_levels}")
        print(f"  数据形状: {X_data.shape}")
        print(f"  标签范围: [{y_mapped.min()}, {y_mapped.max()}]")
        print(f"  SNR范围: [{snr_values.min()}, {snr_values.max()}]")

        # 划分数据集
        train_ratio, test_ratio, val_ratio = train_test_val_split
        temp_ratio = test_ratio + val_ratio

        X_train, X_temp, y_train, y_temp, snr_train, snr_temp = train_test_split(
            X_data, y_mapped, snr_values,
            test_size=temp_ratio,
            random_state=42,
            stratify=y_mapped
        )

        # 然后划分测试集和验证集
        test_ratio_adjusted = test_ratio / temp_ratio
        X_test, X_val, y_test, y_val, snr_test, snr_val = train_test_split(
            X_temp, y_temp, snr_temp,
            test_size=(1 - test_ratio_adjusted),
            random_state=42,
            stratify=y_temp
        )

        print(f"数据集划分完成:")
        print(f"  训练集: {len(X_train)} 样本 ({len(X_train)/len(X_data)*100:.1f}%)")
        print(f"  测试集: {len(X_test)} 样本 ({len(X_test)/len(X_data)*100:.1f}%)")
        print(f"  验证集: {len(X_val)} 样本 ({len(X_val)/len(X_data)*100:.1f}%)")

        return (X_train, y_train, snr_train), (X_test, y_test, snr_test), (X_val, y_val, snr_val)

    except Exception as e:
        print(f"加载RML2018.01a数据集时出错: {e}")
        raise


def create_stratified_indices(dataset, train_ratio=0.7, seed=42):
    """
    创建分层采样的训练和验证索引

    Args:
        dataset: 数据集对象，需要有labels属性
        train_ratio: 训练集比例
        seed: 随机种子

    Returns:
        train_indices, val_indices: 训练和验证索引列表
    """
    np.random.seed(seed)

    # 获取所有标签
    if hasattr(dataset, 'labels'):
        labels = dataset.labels.numpy() if isinstance(dataset.labels, torch.Tensor) else dataset.labels
    else:
        # 如果没有labels属性，尝试通过遍历获取
        labels = []
        for i in range(len(dataset)):
            _, label, _ = dataset[i]
            labels.append(label.item() if isinstance(label, torch.Tensor) else label)
        labels = np.array(labels)

    # 获取唯一标签
    unique_labels = np.unique(labels)

    train_indices = []
    val_indices = []

    # 对每个类别进行分层采样
    for label in unique_labels:
        label_indices = np.where(labels == label)[0]
        np.random.shuffle(label_indices)

        split_idx = int(len(label_indices) * train_ratio)
        train_indices.extend(label_indices[:split_idx])
        val_indices.extend(label_indices[split_idx:])

    return train_indices, val_indices


def get_hisar_data_loaders(config, num_workers=1, pin_memory=True, persist_workers=False):
    """创建Hisar数据集的数据加载器"""
    batch_size = config['training']['batch_size']
    sequence_length = config['model']['sequence_length']
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    if 'device' in config.get('training', {}):
        device = config['training']['device']

    # 获取数据文件路径
    train_path = config['data']['train_path']
    train_labels_path = config['data']['train_labels_path']
    train_snr_path = config['data']['train_snr_path']

    test_path = config['data']['test_path']
    test_labels_path = config['data']['test_labels_path']
    test_snr_path = config['data']['test_snr_path']

    print(f"使用HisarMod数据集，序列长度: {sequence_length}")

    # 加载训练数据集
    train_dataset = HisarModDataset(
        train_path,
        labels_path=train_labels_path,
        snr_path=train_snr_path,
        sequence_length=sequence_length,
        is_training=True,
        device='cpu'  # 初始加载在CPU上，在DataLoader中转移到GPU
    )

    # 创建训练和验证索引
    train_indices, val_indices = create_stratified_indices(
        train_dataset,
        train_ratio=config['data'].get('train_ratio', 0.7),
        seed=config['training'].get('seed', 42)
    )

    print(f"数据集划分: 训练集 {len(train_indices)} 样本, 验证集 {len(val_indices)} 样本")

    # 创建子集
    from torch.utils.data import Subset
    train_subset = Subset(train_dataset, train_indices)
    val_subset = Subset(train_dataset, val_indices)

    # 设置预取因子和持久化工作进程
    prefetch_factor = 2 if num_workers > 0 else None
    persistent_workers = persist_workers and num_workers > 0

    # 创建数据加载器
    train_loader = DataLoader(
        train_subset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=pin_memory,
        prefetch_factor=prefetch_factor,
        persistent_workers=persistent_workers
    )

    val_loader = DataLoader(
        val_subset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=pin_memory,
        prefetch_factor=prefetch_factor,
        persistent_workers=persistent_workers
    )

    # 加载测试集
    test_dataset = HisarModDataset(
        test_path,
        labels_path=test_labels_path,
        snr_path=test_snr_path,
        sequence_length=sequence_length,
        is_training=False,
        device='cpu'
    )

    test_loader = DataLoader(
        test_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=pin_memory,
        prefetch_factor=prefetch_factor,
        persistent_workers=persistent_workers
    )

    print(f"数据加载完成，批次大小: {batch_size}")
    return train_loader, val_loader, test_loader


def get_torchsig_data_loaders(config, num_workers=1, pin_memory=True, persist_workers=False):
    """创建TorchSig数据集的数据加载器，支持不同序列长度(1024/2048/4096)"""
    batch_size = config['training']['batch_size']
    sequence_length = config['model']['sequence_length']
    dataset_type = config['data'].get('dataset_type', 'torchsig1024')
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    if 'device' in config.get('training', {}):
        device = config['training']['device']

    # 设置预取因子和持久化工作进程
    prefetch_factor = 2 if num_workers > 0 else None
    persistent_workers = persist_workers and num_workers > 0

    print(f"使用TorchSig数据集，序列长度: {sequence_length}")

    # 根据数据集类型构造文件路径
    base_dir = f"data/{dataset_type}"

    # 获取数据文件路径
    train_path = config['data'].get(f'{dataset_type}_train_path', f'{base_dir}/train_data.mat')
    train_labels_path = config['data'].get(f'{dataset_type}_train_labels_path', f'{base_dir}/train_labels.csv')
    train_snr_path = config['data'].get(f'{dataset_type}_train_snr_path', f'{base_dir}/train_snr.csv')

    test_path = config['data'].get(f'{dataset_type}_test_path', f'{base_dir}/test_data.mat')
    test_labels_path = config['data'].get(f'{dataset_type}_test_labels_path', f'{base_dir}/test_labels.csv')
    test_snr_path = config['data'].get(f'{dataset_type}_test_snr_path', f'{base_dir}/test_snr.csv')

    # 创建TorchSig数据集类（继承自HisarModDataset，但添加标签映射）
    class TorchSigDataset(HisarModDataset):
        def __init__(self, data_path, labels_path, snr_path, sequence_length=1024, is_training=True, device='cpu'):
            # TorchSig标签映射（25个调制类型）
            self.label_map = {
                0: 'ook', 1: 'bpsk', 2: 'qpsk', 3: '8psk', 4: '16psk',
                5: '32psk', 6: '16apsk', 7: '32apsk', 8: '64apsk', 9: '128apsk',
                10: '16qam', 11: '32qam', 12: '64qam', 13: '128qam', 14: '256qam',
                15: 'am-ssb-wc', 16: 'am-ssb-sc', 17: 'am-dsb-wc', 18: 'am-dsb-sc',
                19: 'fm', 20: 'gmsk', 21: 'oqpsk', 22: 'msk', 23: 'gfsk', 24: 'cpfsk'
            }

            # 获取排序后的标签键
            sorted_labels = sorted(self.label_map.keys())

            # 从标签映射中提取调制类型列表
            self.mod_types = [self.label_map[k] for k in sorted_labels]

            # 调用父类方法加载数据
            super().__init__(data_path, labels_path, snr_path, sequence_length, is_training, device)

    # 加载TorchSig训练数据集
    print(f"加载训练集...")
    train_dataset = TorchSigDataset(
        train_path,
        labels_path=train_labels_path,
        snr_path=train_snr_path,
        sequence_length=sequence_length,
        is_training=True,
        device='cpu'  # 初始加载在CPU上，在DataLoader中转移到GPU
    )

    # 创建训练和验证索引
    train_indices, val_indices = create_stratified_indices(
        train_dataset,
        train_ratio=config['data'].get('train_ratio', 0.7),
        seed=config['training'].get('seed', 42)
    )

    print(f"数据集划分: 训练集 {len(train_indices)} 样本, 验证集 {len(val_indices)} 样本")

    # 创建子集
    from torch.utils.data import Subset
    train_subset = Subset(train_dataset, train_indices)
    val_subset = Subset(train_dataset, val_indices)

    train_loader = DataLoader(
        train_subset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=pin_memory,
        prefetch_factor=prefetch_factor,
        persistent_workers=persistent_workers
    )

    val_loader = DataLoader(
        val_subset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=pin_memory,
        prefetch_factor=prefetch_factor,
        persistent_workers=persistent_workers
    )

    # 加载测试集
    print(f"加载测试集...")
    test_dataset = TorchSigDataset(
        test_path,
        labels_path=test_labels_path,
        snr_path=test_snr_path,
        sequence_length=sequence_length,
        is_training=False,
        device='cpu'
    )

    test_loader = DataLoader(
        test_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=pin_memory,
        prefetch_factor=prefetch_factor,
        persistent_workers=persistent_workers
    )

    print(f"数据加载完成，批次大小: {batch_size}")
    return train_loader, val_loader, test_loader


def get_rml201801a_data_loaders(config, num_workers=1, pin_memory=True, persist_workers=False):
    """创建RML2018.01a数据集的数据加载器"""
    batch_size = config['training']['batch_size']

    # 获取数据文件路径
    data_file = config['data'].get('rml201801a_file_path', 'data/GOLD_XYZ_OSC.0001_1024.hdf5')

    # 获取调制类型配置
    modulations = config['data'].get('rml201801a_modulations', None)  # None表示使用所有调制类型
    use_all_snr = config['data'].get('rml201801a_use_all_snr', True)  # 是否使用所有SNR级别

    # 获取数据集划分比例
    train_ratio = config['data'].get('train_ratio', 0.7)
    test_ratio = config['data'].get('test_ratio', 0.15)
    val_ratio = config['data'].get('val_ratio', 0.15)

    # 获取大内存优化参数
    preload_to_memory = config['training'].get('preload_to_memory', False)

    print(f"RML2018.01a数据集配置:")
    print(f"  数据文件: {data_file}")
    print(f"  使用所有SNR: {use_all_snr}")
    print(f"  数据集划分: 训练{train_ratio*100:.0f}%, 测试{test_ratio*100:.0f}%, 验证{val_ratio*100:.0f}%")
    print(f"  预加载到内存: {preload_to_memory}")
    if modulations:
        print(f"  选择的调制类型: {modulations}")
    else:
        print(f"  使用所有调制类型")

    # 加载数据集
    (X_train, y_train, snr_train), (X_test, y_test, snr_test), (X_val, y_val, snr_val) = load_rml201801a_dataset(
        file_path=data_file,
        modulations=modulations,
        use_all_snr=use_all_snr,
        train_test_val_split=(train_ratio, test_ratio, val_ratio)
    )

    # 根据配置选择设备
    device = 'cpu'
    if preload_to_memory and torch.cuda.is_available():
        # 检查GPU内存是否足够
        gpu_memory = torch.cuda.get_device_properties(0).total_memory
        estimated_memory = X_train.nbytes + X_val.nbytes + X_test.nbytes
        if estimated_memory < gpu_memory * 0.5:  # 使用不超过50%的GPU内存
            device = 'cuda'
            print(f"  数据将预加载到GPU内存 (估计使用 {estimated_memory/1024**3:.1f} GB)")
        else:
            print(f"  GPU内存不足，数据保持在CPU内存")

    # 创建数据集
    train_dataset = RML201801aDataset(X_train, y_train, snr_train, device=device)
    val_dataset = RML201801aDataset(X_val, y_val, snr_val, device=device)
    test_dataset = RML201801aDataset(X_test, y_test, snr_test, device=device)

    # 优化配置参数
    prefetch_factor = config['training'].get('prefetch_factor', 2)
    multiprocessing_context = config['training'].get('multiprocessing_context', None)
    drop_last = config['training'].get('drop_last', False)

    # 对于大型数据集，启用persistent_workers以提高性能
    persistent_workers = config['training'].get('persistent_workers', persist_workers)
    if num_workers > 0:
        persistent_workers = True
    else:
        persistent_workers = False

    # 如果数据已经在GPU上，减少num_workers以避免不必要的数据传输
    if device == 'cuda':
        num_workers = min(num_workers, 2)
        pin_memory = False  # 数据已经在GPU上，不需要pin_memory
        print(f"  数据在GPU上，调整num_workers为{num_workers}，禁用pin_memory")

    # 创建数据加载器参数
    loader_kwargs = {
        'batch_size': batch_size,
        'num_workers': num_workers,
        'pin_memory': pin_memory,
        'persistent_workers': persistent_workers,
        'drop_last': drop_last
    }

    # 添加可选参数
    if num_workers > 0:
        loader_kwargs['prefetch_factor'] = prefetch_factor
    if multiprocessing_context and num_workers > 0:
        loader_kwargs['multiprocessing_context'] = multiprocessing_context

    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        shuffle=True,
        **loader_kwargs
    )

    val_loader = DataLoader(
        val_dataset,
        shuffle=False,
        **loader_kwargs
    )

    test_loader = DataLoader(
        test_dataset,
        shuffle=False,
        **loader_kwargs
    )

    print(f"RML2018.01a数据加载完成，批次大小: {batch_size}")
    print(f"  数据加载器配置: num_workers={num_workers}, prefetch_factor={prefetch_factor}")
    return train_loader, val_loader, test_loader
