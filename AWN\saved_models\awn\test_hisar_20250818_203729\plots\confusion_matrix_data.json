{"confusion_matrix": [[9380, 37, 0, 64, 0, 62, 34, 66, 32, 0, 63, 6, 35, 34, 1, 56, 9, 19, 0, 27, 2, 2, 65, 4, 0, 2], [131, 5572, 0, 16, 0, 1640, 58, 18, 12, 0, 493, 156, 53, 7, 0, 463, 139, 61, 0, 306, 160, 0, 499, 124, 0, 92], [0, 0, 9909, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13, 0, 0, 0, 7, 0, 0, 33, 0, 0, 38, 0], [63, 4, 0, 7757, 0, 16, 222, 14, 813, 0, 11, 31, 40, 828, 0, 6, 33, 50, 0, 9, 32, 0, 8, 33, 0, 30], [0, 0, 0, 0, 9999, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [154, 995, 0, 19, 0, 6178, 95, 23, 13, 0, 505, 146, 62, 10, 0, 448, 144, 70, 0, 298, 129, 1, 454, 157, 0, 99], [45, 30, 0, 255, 0, 52, 7728, 8, 234, 0, 29, 256, 35, 242, 0, 45, 246, 53, 0, 38, 285, 0, 60, 167, 0, 192], [26, 3, 0, 4, 0, 6, 0, 9729, 0, 0, 7, 1, 160, 4, 0, 2, 2, 50, 0, 1, 0, 0, 3, 0, 0, 2], [27, 2, 0, 806, 0, 9, 160, 12, 7340, 0, 2, 17, 34, 1420, 0, 4, 30, 34, 0, 2, 50, 0, 6, 17, 0, 28], [0, 1, 0, 0, 0, 1, 0, 0, 0, 9995, 0, 0, 1, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0], [85, 289, 0, 12, 0, 398, 65, 17, 13, 1, 4970, 328, 55, 7, 0, 1153, 271, 66, 0, 784, 236, 1, 860, 238, 0, 151], [21, 116, 0, 79, 0, 132, 355, 11, 68, 0, 232, 5147, 23, 36, 0, 148, 974, 67, 0, 134, 1045, 0, 167, 661, 0, 584], [28, 13, 0, 22, 0, 19, 14, 410, 12, 0, 21, 6, 8327, 15, 0, 8, 22, 1033, 1, 11, 6, 2, 12, 12, 0, 6], [20, 3, 0, 745, 0, 4, 160, 2, 1324, 0, 5, 9, 17, 7551, 0, 2, 33, 35, 0, 2, 39, 0, 1, 16, 0, 32], [0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9574, 0, 0, 0, 61, 0, 0, 174, 0, 0, 187, 0], [72, 268, 0, 11, 0, 376, 93, 10, 16, 0, 970, 285, 33, 10, 0, 4948, 281, 101, 0, 929, 207, 0, 972, 261, 0, 157], [13, 77, 0, 38, 0, 107, 319, 6, 49, 0, 194, 880, 22, 39, 0, 165, 4957, 40, 0, 133, 1145, 0, 244, 872, 0, 700], [25, 12, 0, 20, 0, 22, 13, 159, 31, 0, 27, 15, 1295, 25, 0, 21, 17, 8237, 0, 21, 14, 0, 14, 15, 0, 17], [0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 93, 0, 0, 0, 9535, 0, 0, 214, 0, 0, 156, 0], [101, 299, 0, 16, 0, 368, 88, 13, 12, 0, 1103, 278, 46, 20, 0, 1178, 273, 67, 0, 4441, 272, 0, 1040, 230, 0, 155], [15, 65, 0, 65, 0, 70, 416, 4, 95, 0, 103, 825, 36, 65, 0, 106, 878, 73, 0, 104, 5372, 0, 124, 795, 0, 789], [0, 0, 4, 0, 3, 1, 0, 0, 0, 0, 0, 0, 0, 0, 246, 0, 0, 1, 230, 0, 0, 7446, 0, 0, 2069, 0], [95, 294, 0, 18, 0, 364, 78, 14, 10, 0, 945, 241, 52, 11, 1, 1274, 344, 69, 0, 865, 197, 2, 4775, 188, 0, 163], [15, 98, 0, 48, 0, 129, 348, 6, 67, 0, 168, 766, 24, 58, 0, 160, 938, 67, 0, 111, 1220, 0, 185, 4713, 0, 879], [0, 0, 7, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 342, 0, 0, 0, 295, 0, 0, 2674, 0, 0, 6682, 0], [12, 64, 0, 79, 0, 69, 401, 10, 76, 0, 95, 734, 29, 62, 0, 106, 841, 55, 0, 79, 1440, 0, 139, 979, 0, 4730]], "class_names": ["BPSK", "QPSK", "8PSK", "16PSK", "32PSK", "64PSK", "4QAM", "8QAM", "16QAM", "32QAM", "64QAM", "128QAM", "256QAM", "2FSK", "4FSK", "8FSK", "16FSK", "4PAM", "8PAM", "16PAM", "AM-DSB", "AM-DSB-SC", "AM-USB", "AM-LSB", "FM", "PM"]}