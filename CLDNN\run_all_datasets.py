#!/usr/bin/env python3
"""
CLDNN多数据集自动训练脚本

这个脚本会自动在所有支持的数据集上训练CLDNN模型，并实时显示详细的训练信息。
支持的数据集: RML, RML2018.01a, <PERSON><PERSON>, TorchSig1024, TorchSig2048, TorchSig4096

特点:
- 实时显示训练过程的详细输出
- 详细的训练时间统计
- 完整的成功/失败状态报告
- 自动清理临时配置文件

使用方法:
    python run_all_datasets.py

注意
- 每个数据集会顺序执行，无论前一个是否早停
- 训练过程中的所有输出都会实时显示并记录到日志文
"""

# ========================================================================
# 🎯 要训练的数据集配置区域 🎯
# ========================================================================
# 在这里直接修改你想要训练的数据集列表
# 支持的数据集: 'rml', 'rml201801a', 'hisar', 'torchsig1024', 'torchsig2048', 'torchsig4096'
# 注释掉不需要训练的数据集，或者直接删除对应行
DEFAULT_DATASETS_TO_TRAIN = [
    'rml',           # RML数据集
    'rml201801a',    # RML2018.01a数据集
    'hisar',         # Hisar数据集
    'torchsig1024',  # TorchSig1024数据集
    'torchsig2048',  # TorchSig2048数据集
    'torchsig4096'   # TorchSig4096数据集
]
# ========================================================================

# ========================================================================
# 🔥 自定义数据集批处理大小配置区域 🔥
# ========================================================================
# 为了提高训练效率，可以为不同数据集设置不同的batch size
# 根据数据集大小和复杂度调整以下数值
DATASET_BATCH_SIZES = {
    'rml': 512,           # RML数据集 - 大内存环境使用更大batch size
    'rml201801a': 256,    # RML2018.01a数据集 - 大数据集，大内存环境优化
    'hisar': 512,         # Hisar数据集 - 大内存环境使用更大batch size
    'torchsig1024': 512,  # TorchSig1024 - 大内存环境优化
    'torchsig2048': 256,  # TorchSig2048 - 序列较长，适中batch size
    'torchsig4096': 128   # TorchSig4096 - 序列最长，保持较小batch size
}
# ========================================================================

import os
import sys
import yaml
import subprocess
import time
from datetime import datetime
import logging

def setup_logging():
    """设置日志"""
    log_file = f'run_all_datasets_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler(sys.stdout)
        ]
    )
    return logging.getLogger(__name__)

def load_base_config(config_path='config.yaml'):
    """加载基础配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config

def create_dataset_config(base_config, dataset_type):
    """为特定数据集创建配置"""
    config = base_config.copy()
    config['data']['dataset_type'] = dataset_type

    # 根据数据集类型调整模型参数
    if dataset_type == 'rml':
        config['model']['num_classes'] = len(config['rml_class_names'])
        config['model']['sequence_length'] = config['data']['sequence_lengths']['rml']
    elif dataset_type == 'rml201801a':
        config['model']['num_classes'] = len(config['rml201801a_class_names'])
        config['model']['sequence_length'] = config['data']['sequence_lengths']['rml201801a']
    elif dataset_type == 'hisar':
        config['model']['num_classes'] = len(config['hisar_class_names'])
        config['model']['sequence_length'] = config['data']['sequence_lengths']['hisar']
    elif dataset_type.startswith('torchsig'):
        config['model']['num_classes'] = len(config['torchsig_class_names'])
        config['model']['sequence_length'] = config['data']['sequence_lengths'][dataset_type]

    # 🔥 设置数据集特定的batch size 🔥
    if dataset_type in DATASET_BATCH_SIZES:
        config['training']['batch_size'] = DATASET_BATCH_SIZES[dataset_type]
        print(f"🔥 为{dataset_type}设置批处理大小: {DATASET_BATCH_SIZES[dataset_type]} 🔥")
    else:
        print(f"⚠️ 未找到{dataset_type}的批处理大小配置，使用默认值")

    return config

def save_config(config, config_path):
    """保存配置文件"""
    with open(config_path, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)

def run_training(dataset_type, config_path, logger):
    """运行单个数据集的训练"""
    logger.info(f"\n{'='*60}")
    logger.info(f"开始训练数据集: {dataset_type.upper()}")
    logger.info(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info(f"{'='*60}\n")

    # 构建训练命令
    cmd = [sys.executable, 'train.py', '--config', config_path]
    logger.info(f"执行命令: {' '.join(cmd)}")
    logger.info(f"工作目录: {os.getcwd()}")

    # 记录开始时间
    start_time = time.time()

    try:
        # 执行训练 - 实时显示输出
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True
        )

        # 实时显示输出
        for line in process.stdout:
            print(line, end='')
            # 同时写入日志（去掉换行符避免重复）
            logger.info(line.rstrip())

        # 等待进程完成
        process.wait()

        # 计算训练时间
        end_time = time.time()
        training_time = end_time - start_time

        if process.returncode == 0:
            logger.info(f"\n{'='*60}")
            logger.info(f"数据集 {dataset_type.upper()} 训练成功完成")
            logger.info(f"训练时间: {training_time/60:.1f} 分钟 ({training_time:.1f} 秒)")
            logger.info(f"{'='*60}\n")
            return True, training_time
        else:
            logger.error(f"\n{'='*60}")
            logger.error(f"数据集 {dataset_type.upper()} 训练失败")
            logger.error(f"返回码: {process.returncode}")
            logger.error(f"训练时间: {training_time/60:.1f} 分钟")
            logger.error(f"{'='*60}\n")
            return False, training_time

    except Exception as e:
        end_time = time.time()
        training_time = end_time - start_time
        logger.error(f"\n训练过程中发生异常: {e}")
        logger.error(f"训练时间: {training_time/60:.1f} 分钟")
        return False, training_time

def main():
    """主函数"""
    logger = setup_logging()
    logger.info("开始CLDNN多数据集自动训练")

    # 使用文件顶部配置的数据集列表
    datasets = DEFAULT_DATASETS_TO_TRAIN

    # 加载基础配置
    try:
        base_config = load_base_config()
        logger.info("基础配置加载成功")
    except Exception as e:
        logger.error(f"加载基础配置失败: {e}")
        return

    # 创建临时配置目录
    temp_config_dir = 'temp_configs'
    os.makedirs(temp_config_dir, exist_ok=True)

    # 训练结果统计
    results = {}
    training_times = {}
    successful_datasets = []
    failed_datasets = []
    total_start_time = time.time()

    logger.info(f"\n{'='*60}")
    logger.info(f"CLDNN 多数据集训练开始")
    logger.info(f"总共要训练的数据集: {len(datasets)}")
    logger.info(f"数据集列表: {', '.join(datasets)}")
    logger.info(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info(f"{'='*60}")

    for i, dataset in enumerate(datasets, 1):
        logger.info(f"\n进度: [{i}/{len(datasets)}] 准备训练数据集: {dataset}")

        try:
            # 创建数据集特定的配置
            dataset_config = create_dataset_config(base_config, dataset)

            # 保存临时配置文件
            temp_config_path = os.path.join(temp_config_dir, f'config_{dataset}.yaml')
            save_config(dataset_config, temp_config_path)
            logger.info(f"数据集配置保存到: {temp_config_path}")

            # 运行训练
            success, training_time = run_training(dataset, temp_config_path, logger)
            training_times[dataset] = training_time

            if success:
                results[dataset] = 'SUCCESS'
                successful_datasets.append(dataset)
            else:
                results[dataset] = 'FAILED'
                failed_datasets.append(dataset)
                logger.error(f"数据集 {dataset} 训练失败，继续下一个...")

            # 清理临时配置文件
            if os.path.exists(temp_config_path):
                os.remove(temp_config_path)

        except Exception as e:
            logger.error(f"处理数据集 {dataset} 时出现异常: {e}")
            results[dataset] = 'ERROR'
            failed_datasets.append(dataset)
            training_times[dataset] = 0
    
    # 计算总训练时间
    total_end_time = time.time()
    total_training_time = total_end_time - total_start_time

    # 打印训练结果摘要
    logger.info(f"\n{'='*60}")
    logger.info(f"CLDNN 多数据集训练完成")
    logger.info(f"{'='*60}")
    logger.info(f"总训练时间: {total_training_time/3600:.1f} 小时 ({total_training_time/60:.1f} 分钟)")
    logger.info(f"完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    logger.info(f"\n成功训练的数据集 ({len(successful_datasets)}/{len(datasets)}):")
    for ds in successful_datasets:
        time_str = f"{training_times[ds]/60:.1f} 分钟"
        logger.info(f"  ✓ {ds.upper()} - {time_str}")

    if failed_datasets:
        logger.info(f"\n失败的数据集 ({len(failed_datasets)}/{len(datasets)}):")
        for ds in failed_datasets:
            time_str = f"{training_times.get(ds, 0)/60:.1f} 分钟"
            logger.info(f"  ✗ {ds.upper()} - {time_str}")

    # 清理临时配置文件
    logger.info(f"\n清理临时配置文件...")
    for dataset_type in datasets:
        temp_config_path = os.path.join(temp_config_dir, f'config_{dataset_type}.yaml')
        if os.path.exists(temp_config_path):
            os.remove(temp_config_path)
            logger.info(f"  删除: {temp_config_path}")

    # 如果临时目录为空，删除它
    try:
        os.rmdir(temp_config_dir)
        logger.info(f"  删除临时目录: {temp_config_dir}")
    except OSError:
        pass  # 目录不为空或不存在

    logger.info(f"\n{'='*60}")
    if len(successful_datasets) == len(datasets):
        logger.info("🎉 所有数据集训练成功完成！")
    elif successful_datasets:
        logger.info(f"⚠️  部分数据集训练完成 ({len(successful_datasets)}/{len(datasets)})")
    else:
        logger.error("❌ 所有数据集训练失败")
    logger.info(f"{'='*60}")

if __name__ == '__main__':
    main()
