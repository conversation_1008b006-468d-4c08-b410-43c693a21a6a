#!/usr/bin/env python3
"""
WNN-MRNN模型复杂度分析器

专门用于测量模型参数量、MAC计算量以及各个结构的详细分析
使用方法：python model_complexity_analyzer.py --config config.yaml --model_path saved_models/best_model.pth
"""

import os
import sys
import yaml
import torch
import torch.nn as nn
import argparse
import numpy as np
import logging
from datetime import datetime
import json
from collections import OrderedDict

# 尝试导入计算MACs的库
try:
    from thop import profile, clever_format
    THOP_AVAILABLE = True
except ImportError:
    THOP_AVAILABLE = False

try:
    from ptflops import get_model_complexity_info
    PTFLOPS_AVAILABLE = True
except ImportError:
    PTFLOPS_AVAILABLE = False

try:
    from fvcore.nn import FlopCountMode, flop_count
    FVCORE_AVAILABLE = True
except ImportError:
    FVCORE_AVAILABLE = False

# 导入模型和工具
from models import WNN_MRNN
from utils.dataset import (
    load_rml2016_data, load_rml201801a_data, load_hisar_data,
    load_torchsig_data, RML2016Dataset
)

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )
    return logging.getLogger(__name__)

def load_config(config_path):
    """加载配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config

def get_test_data_loader(config):
    """获取测试数据加载器（简化版，只用于获取数据形状）"""
    dataset_type = config['data']['dataset_type']
    
    if dataset_type == 'rml':
        X_test, y_test, snr_test = load_rml2016_data(config, split='test')
    elif dataset_type == 'rml201801a':
        X_test, y_test, snr_test = load_rml201801a_data(config, split='test')
    elif dataset_type == 'hisar':
        X_test, y_test, snr_test = load_hisar_data(config, split='test')
    elif dataset_type.startswith('torchsig'):
        X_test, y_test, snr_test = load_torchsig_data(config, split='test')
    else:
        raise ValueError(f"不支持的数据集类型: {dataset_type}")
    
    # 创建简单的测试数据集（只取少量样本用于测试）
    test_dataset = RML2016Dataset(X_test[:100], y_test[:100], snr_test[:100])
    test_loader = torch.utils.data.DataLoader(test_dataset, batch_size=32, shuffle=False)
    
    return test_loader

def load_model(model_path, config, device):
    """加载训练好的模型"""
    logger = logging.getLogger(__name__)
    checkpoint = torch.load(model_path, map_location=device)
    
    # 从检查点获取配置（如果有的话）
    if 'config' in checkpoint:
        saved_config = checkpoint['config']
        model_config = saved_config['model']
        dataset_type = saved_config['data']['dataset_type']
        data_config = saved_config['data']
        logger.info("使用检查点中保存的配置")
    else:
        model_config = config['model']
        dataset_type = config['data']['dataset_type']
        data_config = config['data']
        logger.info("使用当前配置文件")
    
    # 确定类别数和序列长度
    if dataset_type == 'rml':
        num_classes = len(config['rml_class_names'])
        sequence_length = data_config['sequence_lengths']['rml']
    elif dataset_type == 'rml201801a':
        num_classes = len(config['rml201801a_class_names'])
        sequence_length = data_config['sequence_lengths']['rml201801a']
    elif dataset_type == 'hisar':
        num_classes = len(config['hisar_class_names'])
        sequence_length = data_config['sequence_lengths']['hisar']
    elif dataset_type.startswith('torchsig'):
        num_classes = len(config['torchsig_class_names'])
        sequence_length = data_config['sequence_lengths'][dataset_type]
    else:
        raise ValueError(f"不支持的数据集类型: {dataset_type}")
    
    # 获取数据集特定的模型参数
    if 'dataset_specific_params' in model_config:
        dataset_params = model_config['dataset_specific_params'].get(dataset_type, {})
        wavelet_dim = dataset_params.get('wavelet_dim', 96)
        rnn_dim = dataset_params.get('rnn_dim', 160)
        num_layers = dataset_params.get('num_layers', model_config.get('num_layers', 2))
        num_levels = dataset_params.get('num_levels', model_config.get('num_levels', 3))
        dropout = dataset_params.get('dropout', model_config.get('dropout', 0.1))
    else:
        # 兼容旧版本配置
        wavelet_dim = model_config.get('wavelet_dim', 96)
        rnn_dim = model_config.get('rnn_dim', 160)
        num_layers = model_config.get('num_layers', 2)
        num_levels = model_config.get('num_levels', 3)
        dropout = model_config.get('dropout', 0.1)
    
    logger.info(f"模型参数配置:")
    logger.info(f"  数据集: {dataset_type}")
    logger.info(f"  类别数: {num_classes}")
    logger.info(f"  序列长度: {sequence_length}")
    logger.info(f"  小波维度: {wavelet_dim}")
    logger.info(f"  RNN维度: {rnn_dim}")
    logger.info(f"  MMRNN层数: {num_layers}")
    logger.info(f"  小波分解层数: {num_levels}")
    logger.info(f"  Dropout: {dropout}")
    logger.info(f"  其他参数: in_channels={model_config.get('in_channels', 2)}, msb_depth={model_config.get('msb_depth', 1)}")
    logger.info(f"  Mamba参数: d_state={model_config.get('d_state', 16)}, d_conv={model_config.get('d_conv', 4)}, expand={model_config.get('expand', 2)}")
    
    # 创建模型
    model = WNN_MRNN(
        in_channels=model_config.get('in_channels', 2),
        num_classes=num_classes,
        wavelet_dim=wavelet_dim,
        rnn_dim=rnn_dim,
        num_layers=num_layers,
        num_levels=num_levels,
        msb_depth=model_config.get('msb_depth', 1),
        drop_rate=dropout,
        d_state=model_config.get('d_state', 16),
        d_conv=model_config.get('d_conv', 4),
        expand=model_config.get('expand', 2)
    )
    
    # 加载权重
    try:
        model.load_state_dict(checkpoint['model_state_dict'], strict=True)
        logger.info("✅ 模型权重加载成功（严格模式）")
    except RuntimeError as e:
        logger.warning(f"⚠️ 严格模式加载失败: {e}")
        logger.info("🔄 尝试使用非严格模式加载...")
        
        missing_keys, unexpected_keys = model.load_state_dict(checkpoint['model_state_dict'], strict=False)
        
        if missing_keys:
            logger.warning(f"⚠️ 缺失的键: {missing_keys}")
        if unexpected_keys:
            logger.warning(f"⚠️ 意外的键: {unexpected_keys}")
        
        logger.info("✅ 模型权重加载成功（非严格模式）")
    
    model = model.to(device)
    return model, sequence_length

def analyze_model_parameters(model):
    """详细分析模型参数"""
    logger = logging.getLogger(__name__)
    
    logger.info("=" * 80)
    logger.info("详细参数分析")
    logger.info("=" * 80)
    
    total_params = 0
    trainable_params = 0
    param_details = OrderedDict()
    
    for name, param in model.named_parameters():
        param_count = param.numel()
        total_params += param_count
        
        if param.requires_grad:
            trainable_params += param_count
        
        param_details[name] = {
            'shape': list(param.shape),
            'parameters': param_count,
            'trainable': param.requires_grad
        }
        
        logger.info(f"{name:50s} | Shape: {str(param.shape):20s} | Params: {param_count:>10,} | Trainable: {param.requires_grad}")
    
    logger.info("-" * 80)
    logger.info(f"{'总参数数量':50s} | {total_params:>10,}")
    logger.info(f"{'可训练参数数量':50s} | {trainable_params:>10,}")
    logger.info(f"{'不可训练参数数量':50s} | {total_params - trainable_params:>10,}")
    logger.info(f"{'可训练参数比例':50s} | {trainable_params/total_params*100:>9.2f}%")
    logger.info("=" * 80)
    
    return {
        'total_params': total_params,
        'trainable_params': trainable_params,
        'non_trainable_params': total_params - trainable_params,
        'trainable_ratio': trainable_params/total_params*100,
        'param_details': param_details
    }

def calculate_macs_fvcore(model, input_shape, device):
    """使用fvcore计算MACs"""
    logger = logging.getLogger(__name__)

    try:
        model.eval()
        dummy_input = torch.randn(1, *input_shape).to(device)

        flop_dict, _ = flop_count(model, (dummy_input,), supported_ops=None)
        total_flops = sum(flop_dict.values())

        # 格式化输出
        if total_flops >= 1e9:
            macs_str = f"{total_flops/1e9:.3f}G"
        elif total_flops >= 1e6:
            macs_str = f"{total_flops/1e6:.3f}M"
        elif total_flops >= 1e3:
            macs_str = f"{total_flops/1e3:.3f}K"
        else:
            macs_str = f"{total_flops:.0f}"

        logger.info(f"fvcore计算结果: {macs_str} MACs ({total_flops:,} FLOPs)")

        # 详细的操作分解
        logger.info("fvcore详细操作分解:")
        for op_name, flop_count_val in flop_dict.items():
            if flop_count_val > 0:
                if flop_count_val >= 1e9:
                    flop_str = f"{flop_count_val/1e9:.3f}G"
                elif flop_count_val >= 1e6:
                    flop_str = f"{flop_count_val/1e6:.3f}M"
                elif flop_count_val >= 1e3:
                    flop_str = f"{flop_count_val/1e3:.3f}K"
                else:
                    flop_str = f"{flop_count_val:.0f}"
                logger.info(f"  {op_name}: {flop_str} FLOPs ({flop_count_val:,})")

        return total_flops, macs_str, flop_dict

    except Exception as e:
        logger.error(f"fvcore计算失败: {e}")
        return None, f"fvcore失败: {str(e)}", {}

def calculate_macs_thop(model, input_shape, device):
    """使用thop计算MACs"""
    logger = logging.getLogger(__name__)

    try:
        model.eval()
        dummy_input = torch.randn(1, *input_shape).to(device)

        macs, params = profile(model, inputs=(dummy_input,), verbose=False)
        macs_str, params_str = clever_format([macs, params], "%.3f")

        logger.info(f"thop计算结果: {macs_str} MACs, {params_str} Params")
        logger.info(f"thop原始数值: {macs:,} MACs, {params:,} Params")

        return macs, macs_str, params, params_str

    except Exception as e:
        logger.error(f"thop计算失败: {e}")
        return None, f"thop失败: {str(e)}", None, f"thop失败: {str(e)}"

def calculate_macs_ptflops(model, input_shape, device):
    """使用ptflops计算MACs"""
    logger = logging.getLogger(__name__)

    try:
        # ptflops需要模型在CPU上
        model_cpu = model.cpu()

        macs, params = get_model_complexity_info(
            model_cpu, input_shape,
            as_strings=True,
            print_per_layer_stat=False,
            verbose=False
        )

        # 将模型移回原设备
        model.to(device)

        logger.info(f"ptflops计算结果: {macs} MACs, {params} Params")

        return None, macs, None, params

    except Exception as e:
        logger.error(f"ptflops计算失败: {e}")
        # 确保模型回到原设备
        model.to(device)
        return None, f"ptflops失败: {str(e)}", None, f"ptflops失败: {str(e)}"

def comprehensive_mac_analysis(model, input_shape, device):
    """综合MAC分析"""
    logger = logging.getLogger(__name__)

    logger.info("=" * 80)
    logger.info("MAC计算分析")
    logger.info("=" * 80)
    logger.info(f"输入形状: {input_shape}")
    logger.info(f"设备: {device}")

    results = {}

    # 使用fvcore
    if FVCORE_AVAILABLE:
        logger.info("\n--- fvcore分析 ---")
        fvcore_macs, fvcore_str, fvcore_details = calculate_macs_fvcore(model, input_shape, device)
        results['fvcore'] = {
            'macs_raw': fvcore_macs,
            'macs_str': fvcore_str,
            'details': fvcore_details
        }
    else:
        logger.warning("fvcore库不可用")
        results['fvcore'] = {'error': 'fvcore不可用'}

    # 使用thop
    if THOP_AVAILABLE:
        logger.info("\n--- thop分析 ---")
        thop_macs, thop_macs_str, thop_params, thop_params_str = calculate_macs_thop(model, input_shape, device)
        results['thop'] = {
            'macs_raw': thop_macs,
            'macs_str': thop_macs_str,
            'params_raw': thop_params,
            'params_str': thop_params_str
        }
    else:
        logger.warning("thop库不可用")
        results['thop'] = {'error': 'thop不可用'}

    # 使用ptflops
    if PTFLOPS_AVAILABLE:
        logger.info("\n--- ptflops分析 ---")
        _, ptflops_macs_str, _, ptflops_params_str = calculate_macs_ptflops(model, input_shape, device)
        results['ptflops'] = {
            'macs_str': ptflops_macs_str,
            'params_str': ptflops_params_str
        }
    else:
        logger.warning("ptflops库不可用")
        results['ptflops'] = {'error': 'ptflops不可用'}

    logger.info("=" * 80)

    return results

def quick_accuracy_test(model, test_loader, device):
    """快速准确率测试"""
    logger = logging.getLogger(__name__)

    model.eval()
    correct = 0
    total = 0

    with torch.no_grad():
        for data, target, _ in test_loader:
            data, target = data.to(device), target.to(device)
            outputs = model(data)
            _, predicted = torch.max(outputs.data, 1)
            total += target.size(0)
            correct += (predicted == target).sum().item()

    accuracy = 100 * correct / total
    logger.info(f"快速测试准确率: {accuracy:.2f}% ({correct}/{total})")

    return accuracy

def main():
    # 直接设置路径，无需命令行参数
    config_path = 'config.yaml'
    model_path = './saved_models/wnn_mrnn/torchsig4096_20250821_133252/models/best_model.pth'
    output_path = 'model_complexity_analysis.json'

    # 设置日志
    logger = setup_logging()

    logger.info("WNN-MRNN模型复杂度分析器")
    logger.info(f"配置文件: {config_path}")
    logger.info(f"模型文件: {model_path}")
    logger.info(f"输出文件: {output_path}")

    # 检查库可用性
    logger.info(f"库可用性检查:")
    logger.info(f"  fvcore: {'✅' if FVCORE_AVAILABLE else '❌'}")
    logger.info(f"  thop: {'✅' if THOP_AVAILABLE else '❌'}")
    logger.info(f"  ptflops: {'✅' if PTFLOPS_AVAILABLE else '❌'}")

    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")

    # 加载配置
    config = load_config(config_path)

    # 加载模型
    logger.info("加载模型...")
    model, sequence_length = load_model(model_path, config, device)

    # 获取测试数据
    logger.info("加载测试数据...")
    test_loader = get_test_data_loader(config)

    # 分析模型参数
    param_analysis = analyze_model_parameters(model)

    # MAC分析
    input_shape = (2, sequence_length)
    mac_analysis = comprehensive_mac_analysis(model, input_shape, device)

    # 快速准确率测试
    logger.info("进行快速准确率测试...")
    accuracy = quick_accuracy_test(model, test_loader, device)

    # 汇总结果
    results = {
        'timestamp': datetime.now().isoformat(),
        'model_path': model_path,
        'config_path': config_path,
        'dataset_type': config['data']['dataset_type'],
        'input_shape': input_shape,
        'device': str(device),
        'accuracy': accuracy,
        'parameter_analysis': param_analysis,
        'mac_analysis': mac_analysis,
        'library_availability': {
            'fvcore': FVCORE_AVAILABLE,
            'thop': THOP_AVAILABLE,
            'ptflops': PTFLOPS_AVAILABLE
        }
    }

    # 保存结果
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)

    logger.info(f"分析结果已保存到: {output_path}")

    # 打印总结
    logger.info("\n" + "=" * 80)
    logger.info("分析总结")
    logger.info("=" * 80)
    logger.info(f"模型: {model_path}")
    logger.info(f"数据集: {config['data']['dataset_type']}")
    logger.info(f"输入形状: {input_shape}")
    logger.info(f"快速测试准确率: {accuracy:.2f}%")
    logger.info(f"总参数数量: {param_analysis['total_params']:,}")
    logger.info(f"可训练参数数量: {param_analysis['trainable_params']:,}")

    if 'fvcore' in mac_analysis and 'macs_str' in mac_analysis['fvcore']:
        logger.info(f"fvcore MAC: {mac_analysis['fvcore']['macs_str']}")
    if 'thop' in mac_analysis and 'macs_str' in mac_analysis['thop']:
        logger.info(f"thop MAC: {mac_analysis['thop']['macs_str']}")
    if 'ptflops' in mac_analysis and 'macs_str' in mac_analysis['ptflops']:
        logger.info(f"ptflops MAC: {mac_analysis['ptflops']['macs_str']}")

    logger.info("=" * 80)

if __name__ == '__main__':
    main()
