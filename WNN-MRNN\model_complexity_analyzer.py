#!/usr/bin/env python3
"""
WNN-MRNN模型复杂度分析器

专门用于测量模型参数量、MAC计算量以及各个结构的详细分析
直接运行即可：python model_complexity_analyzer.py
"""

import os
import sys
import yaml
import torch
import torch.nn as nn
import numpy as np
import logging
from datetime import datetime
import json
from collections import OrderedDict

# 尝试导入计算MACs的库
try:
    from thop import profile, clever_format
    THOP_AVAILABLE = True
except ImportError:
    THOP_AVAILABLE = False

try:
    from ptflops import get_model_complexity_info
    PTFLOPS_AVAILABLE = True
except ImportError:
    PTFLOPS_AVAILABLE = False

try:
    from fvcore.nn import FlopCountMode, flop_count
    FVCORE_AVAILABLE = True
except ImportError:
    try:
        # 尝试另一种导入方式
        import fvcore
        from fvcore.nn import flop_count
        FVCORE_AVAILABLE = True
    except ImportError:
        FVCORE_AVAILABLE = False

# 导入模型和工具（使用与test.py相同的导入）
from models import WNN_MRNN
from utils.dataset import (
    load_rml_dataset, split_dataset, RML2016Dataset,
    get_hisar_data_loaders, get_torchsig_data_loaders, get_rml201801a_data_loaders
)

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )
    return logging.getLogger(__name__)

def load_config(config_path):
    """加载配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config

def get_data_loaders(config):
    """根据配置获取数据加载器（复制自test.py）"""
    dataset_type = config['data']['dataset_type']

    if dataset_type == 'rml':
        return get_rml_data_loaders(config)
    elif dataset_type == 'rml201801a':
        return get_rml201801a_data_loaders(config)
    elif dataset_type == 'hisar':
        return get_hisar_data_loaders(config)
    elif dataset_type.startswith('torchsig'):
        return get_torchsig_data_loaders(config)
    else:
        raise ValueError(f"不支持的数据集类型: {dataset_type}")

def get_rml_data_loaders(config):
    """获取RML数据集的数据加载器（复制自test.py）"""
    # 获取RML配置
    rml_config = config['data']
    file_path = rml_config['rml_file_path']

    # 获取调制类型
    if rml_config['modulations'] is None:
        modulations = config['rml_class_names']
    else:
        modulations = rml_config['modulations']

    # 加载数据
    X, labels, snrs = load_rml_dataset(
        file_path=file_path,
        modulations=modulations,
        samples_per_key=rml_config.get('samples_per_key')
    )

    # 分割数据集
    (X_train, y_train, snr_train), (X_test, y_test, snr_test) = split_dataset(
        X, labels, snrs,
        train_ratio=rml_config['train_ratio'],
        seed=config['training']['seed'],
        stratify_by_snr=rml_config['stratify_by_snr']
    )

    # 创建测试数据集（只取少量样本用于快速测试）
    test_dataset = RML2016Dataset(X_test[:100], y_test[:100], snr_test[:100])

    # 创建数据加载器
    dataset_type = config['data']['dataset_type']
    dataset_params = config['model']['dataset_specific_params'].get(dataset_type, {})
    batch_size = dataset_params.get('batch_size', config['training']['batch_size'])
    test_loader = torch.utils.data.DataLoader(test_dataset, batch_size=batch_size, shuffle=False)

    return None, None, test_loader

def load_model(model_path, config, device):
    """加载训练好的模型（复制自test.py）"""
    logger = logging.getLogger(__name__)
    checkpoint = torch.load(model_path, map_location=device)

    # 从检查点获取配置（如果有的话）
    if 'config' in checkpoint:
        saved_config = checkpoint['config']
        # 使用保存的模型配置
        model_config = saved_config['model']
        dataset_type = saved_config['data']['dataset_type']
    else:
        # 使用当前配置
        model_config = config['model']
        dataset_type = config['data']['dataset_type']

    # 根据数据集类型调整参数
    if dataset_type == 'rml':
        num_classes = len(config['rml_class_names'])
    elif dataset_type == 'rml201801a':
        num_classes = len(config['rml201801a_class_names'])
    elif dataset_type == 'hisar':
        num_classes = len(config['hisar_class_names'])
    elif dataset_type.startswith('torchsig'):
        num_classes = len(config['torchsig_class_names'])
    else:
        raise ValueError(f"不支持的数据集类型: {dataset_type}")

    # 获取序列长度
    if 'config' in checkpoint:
        sequence_length = checkpoint['config']['data']['sequence_lengths'][dataset_type]
    else:
        sequence_length = config['data']['sequence_lengths'][dataset_type]

    # 获取数据集特定的模型参数
    if 'dataset_specific_params' in model_config:
        dataset_params = model_config['dataset_specific_params'].get(dataset_type, {})
        wavelet_dim = dataset_params.get('wavelet_dim', 96)
        rnn_dim = dataset_params.get('rnn_dim', 160)
        num_layers = dataset_params.get('num_layers', model_config.get('num_layers', 2))
        num_levels = dataset_params.get('num_levels', model_config.get('num_levels', 3))
        dropout = dataset_params.get('dropout', model_config.get('dropout', 0.1))
    else:
        # 兼容旧版本配置
        wavelet_dim = model_config.get('wavelet_dim', 96)
        rnn_dim = model_config.get('rnn_dim', 160)
        num_layers = model_config.get('num_layers', 2)
        num_levels = model_config.get('num_levels', 3)
        dropout = model_config.get('dropout', 0.1)

    logger.info(f"加载模型 - 数据集: {dataset_type}, 小波维度: {wavelet_dim}, RNN维度: {rnn_dim}")
    logger.info(f"MMRNN层数: {num_layers}, 小波分解层数: {num_levels}")
    logger.info(f"其他参数: dropout={dropout}, num_classes={num_classes}")
    logger.info(f"模型参数: in_channels={model_config.get('in_channels', 2)}, msb_depth={model_config.get('msb_depth', 1)}")
    logger.info(f"Mamba参数: d_state={model_config.get('d_state', 16)}, d_conv={model_config.get('d_conv', 4)}, expand={model_config.get('expand', 2)}")

    # 创建模型
    model = WNN_MRNN(
        in_channels=model_config.get('in_channels', 2),
        num_classes=num_classes,
        wavelet_dim=wavelet_dim,
        rnn_dim=rnn_dim,
        num_layers=num_layers,
        num_levels=num_levels,
        msb_depth=model_config.get('msb_depth', 1),
        drop_rate=dropout,
        d_state=model_config.get('d_state', 16),
        d_conv=model_config.get('d_conv', 4),
        expand=model_config.get('expand', 2)
    )

    # 对于论文比较，我们计算基础模型参数（不包含动态适配器）
    logger.info("=== 论文用途：计算基础模型参数（不含动态适配器） ===")

    # 先计算基础模型参数
    base_total_params = sum(p.numel() for p in model.parameters())
    base_trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)

    logger.info(f"基础模型参数: {base_trainable_params:,} (可训练), {base_total_params:,} (总计)")

    # 检查保存的模型中有哪些适配器
    logger.info("检查模型权重中的适配器...")
    adapter_keys = []
    for key in checkpoint['model_state_dict'].keys():
        if 'hidden_state_adapters' in key:
            adapter_keys.append(key)
            logger.info(f"发现适配器: {key}")

    # 解析适配器信息
    adapters_to_create = set()
    for key in adapter_keys:
        parts = key.split('.')
        if len(parts) >= 6:
            layer_idx = parts[2]  # rnn_layers.X
            adapter_name = parts[4]  # 512_to_256
            if '_to_' in adapter_name:
                source_len, target_len = adapter_name.split('_to_')
                adapters_to_create.add((int(layer_idx), int(source_len), int(target_len)))

    logger.info(f"需要创建的适配器: {adapters_to_create}")

    # 为每个MMRNNCell层创建需要的适配器（用于正确加载权重）
    for layer_idx, source_length, target_length in adapters_to_create:
        if layer_idx < num_layers:
            mmrnn_cell = model.mmrnn_classifier.rnn_layers[layer_idx]
            mmrnn_cell._get_or_create_adapter(source_length, target_length, device=device)

    # 计算包含适配器后的参数
    full_total_params = sum(p.numel() for p in model.parameters())
    full_trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)

    adapter_params = full_trainable_params - base_trainable_params
    logger.info(f"动态适配器参数: {adapter_params:,}")
    logger.info(f"完整模型参数: {full_trainable_params:,} (可训练), {full_total_params:,} (总计)")

    # 加载权重
    try:
        model.load_state_dict(checkpoint['model_state_dict'])
        logger.info("✅ 模型权重加载成功")
    except RuntimeError as e:
        logger.error(f"❌ 模型权重加载失败: {e}")
        raise e

    model = model.to(device)

    return model, sequence_length

def analyze_model_parameters(model):
    """详细分析模型参数，区分基础参数和适配器参数"""
    logger = logging.getLogger(__name__)

    logger.info("=" * 80)
    logger.info("详细参数分析")
    logger.info("=" * 80)

    total_params = 0
    trainable_params = 0
    base_params = 0
    adapter_params = 0
    param_details = OrderedDict()

    for name, param in model.named_parameters():
        param_count = param.numel()
        total_params += param_count

        if param.requires_grad:
            trainable_params += param_count

        # 区分基础参数和适配器参数
        is_adapter = 'hidden_state_adapters' in name
        if is_adapter:
            adapter_params += param_count
        else:
            base_params += param_count

        param_details[name] = {
            'shape': list(param.shape),
            'parameters': param_count,
            'trainable': param.requires_grad,
            'is_adapter': is_adapter
        }

        # 标记适配器参数
        adapter_mark = " [ADAPTER]" if is_adapter else ""
        logger.info(f"{name:50s} | Shape: {str(param.shape):20s} | Params: {param_count:>10,} | Trainable: {param.requires_grad}{adapter_mark}")

    logger.info("-" * 80)
    logger.info(f"{'基础模型参数（论文用）':50s} | {base_params:>10,}")
    logger.info(f"{'动态适配器参数':50s} | {adapter_params:>10,}")
    logger.info(f"{'总参数数量':50s} | {total_params:>10,}")
    logger.info(f"{'可训练参数数量':50s} | {trainable_params:>10,}")
    logger.info(f"{'不可训练参数数量':50s} | {total_params - trainable_params:>10,}")
    logger.info(f"{'可训练参数比例':50s} | {trainable_params/total_params*100:>9.2f}%")
    logger.info("=" * 80)
    logger.info("📝 论文建议：使用基础模型参数进行比较，适配器参数可在文中说明")
    logger.info("=" * 80)

    return {
        'base_params': base_params,
        'adapter_params': adapter_params,
        'total_params': total_params,
        'trainable_params': trainable_params,
        'non_trainable_params': total_params - trainable_params,
        'trainable_ratio': trainable_params/total_params*100,
        'param_details': param_details
    }

def calculate_macs_fvcore(model, input_shape, device):
    """使用fvcore计算MACs"""
    logger = logging.getLogger(__name__)

    try:
        # Mamba模块必须在CUDA上运行，不能移到CPU
        model.eval()
        dummy_input = torch.randn(1, *input_shape).to(device)

        flop_dict, _ = flop_count(model, (dummy_input,), supported_ops=None)
        total_flops = sum(flop_dict.values())

        # 格式化输出
        if total_flops >= 1e9:
            macs_str = f"{total_flops/1e9:.3f}G"
        elif total_flops >= 1e6:
            macs_str = f"{total_flops/1e6:.3f}M"
        elif total_flops >= 1e3:
            macs_str = f"{total_flops/1e3:.3f}K"
        else:
            macs_str = f"{total_flops:.0f}"

        logger.info(f"fvcore计算结果: {macs_str} MACs ({total_flops:,} FLOPs)")

        # 详细的操作分解
        logger.info("fvcore详细操作分解:")
        for op_name, flop_count_val in flop_dict.items():
            if flop_count_val > 0:
                if flop_count_val >= 1e9:
                    flop_str = f"{flop_count_val/1e9:.3f}G"
                elif flop_count_val >= 1e6:
                    flop_str = f"{flop_count_val/1e6:.3f}M"
                elif flop_count_val >= 1e3:
                    flop_str = f"{flop_count_val/1e3:.3f}K"
                else:
                    flop_str = f"{flop_count_val:.0f}"
                logger.info(f"  {op_name}: {flop_str} FLOPs ({flop_count_val:,})")

        return total_flops, macs_str, flop_dict

    except Exception as e:
        logger.error(f"fvcore计算失败: {e}")
        return None, f"fvcore失败: {str(e)}", {}

def calculate_macs_thop(model, input_shape, device):
    """使用thop计算MACs"""
    logger = logging.getLogger(__name__)

    try:
        # Mamba模块必须在CUDA上运行
        model.eval()
        dummy_input = torch.randn(1, *input_shape).to(device)

        macs, params = profile(model, inputs=(dummy_input,), verbose=False)
        macs_str, params_str = clever_format([macs, params], "%.3f")

        logger.info(f"thop计算结果: {macs_str} MACs, {params_str} Params")
        logger.info(f"thop原始数值: {macs:,} MACs, {params:,} Params")

        return macs, macs_str, params, params_str

    except Exception as e:
        logger.error(f"thop计算失败: {e}")
        return None, f"thop失败: {str(e)}", None, f"thop失败: {str(e)}"

def calculate_macs_ptflops(model, input_shape, device):
    """使用ptflops计算MACs"""
    logger = logging.getLogger(__name__)

    try:
        # ptflops与Mamba不兼容，跳过
        logger.warning("ptflops与Mamba模块不兼容，跳过计算")
        return None, "ptflops与Mamba不兼容", None, "ptflops与Mamba不兼容"

    except Exception as e:
        logger.error(f"ptflops计算失败: {e}")
        return None, f"ptflops失败: {str(e)}", None, f"ptflops失败: {str(e)}"

def comprehensive_mac_analysis(model, input_shape, device):
    """综合MAC分析"""
    logger = logging.getLogger(__name__)

    logger.info("=" * 80)
    logger.info("MAC计算分析")
    logger.info("=" * 80)
    logger.info(f"输入形状: {input_shape}")
    logger.info(f"设备: {device}")

    results = {}

    # 使用fvcore
    if FVCORE_AVAILABLE:
        logger.info("\n--- fvcore分析 ---")
        fvcore_macs, fvcore_str, fvcore_details = calculate_macs_fvcore(model, input_shape, device)
        results['fvcore'] = {
            'macs_raw': fvcore_macs,
            'macs_str': fvcore_str,
            'details': fvcore_details
        }
    else:
        logger.warning("fvcore库不可用")
        results['fvcore'] = {'error': 'fvcore不可用'}

    # 使用thop
    if THOP_AVAILABLE:
        logger.info("\n--- thop分析 ---")
        thop_macs, thop_macs_str, thop_params, thop_params_str = calculate_macs_thop(model, input_shape, device)
        results['thop'] = {
            'macs_raw': thop_macs,
            'macs_str': thop_macs_str,
            'params_raw': thop_params,
            'params_str': thop_params_str
        }
    else:
        logger.warning("thop库不可用")
        results['thop'] = {'error': 'thop不可用'}

    # 使用ptflops
    if PTFLOPS_AVAILABLE:
        logger.info("\n--- ptflops分析 ---")
        _, ptflops_macs_str, _, ptflops_params_str = calculate_macs_ptflops(model, input_shape, device)
        results['ptflops'] = {
            'macs_str': ptflops_macs_str,
            'params_str': ptflops_params_str
        }
    else:
        logger.warning("ptflops库不可用")
        results['ptflops'] = {'error': 'ptflops不可用'}

    logger.info("=" * 80)

    return results

def quick_accuracy_test(model, test_loader, device):
    """快速准确率测试"""
    logger = logging.getLogger(__name__)

    model.eval()
    correct = 0
    total = 0

    try:
        with torch.no_grad():
            for i, (data, target, _) in enumerate(test_loader):
                data, target = data.to(device), target.to(device)
                outputs = model(data)
                _, predicted = torch.max(outputs.data, 1)
                total += target.size(0)
                correct += (predicted == target).sum().item()

                # 只测试前几个批次，避免太慢
                if i >= 2:  # 只测试3个批次
                    break

        accuracy = 100 * correct / total
        logger.info(f"快速测试准确率: {accuracy:.2f}% ({correct}/{total})")

        return accuracy

    except Exception as e:
        logger.error(f"准确率测试失败: {e}")
        return 0.0

def main():
    # 直接设置路径，无需命令行参数
    config_path = 'config.yaml'
    model_path = './saved_models/wnn_mrnn/torchsig4096_20250821_133252/models/best_model.pth'
    output_path = 'model_complexity_analysis.json'

    # 设置日志
    logger = setup_logging()

    logger.info("WNN-MRNN模型复杂度分析器")
    logger.info(f"配置文件: {config_path}")
    logger.info(f"模型文件: {model_path}")
    logger.info(f"输出文件: {output_path}")

    # 检查库可用性
    logger.info(f"库可用性检查:")
    logger.info(f"  fvcore: {'✅' if FVCORE_AVAILABLE else '❌'}")
    logger.info(f"  thop: {'✅' if THOP_AVAILABLE else '❌'}")
    logger.info(f"  ptflops: {'✅' if PTFLOPS_AVAILABLE else '❌'}")

    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")

    # 加载配置
    config = load_config(config_path)

    # 加载模型
    logger.info("加载模型...")
    model, sequence_length = load_model(model_path, config, device)

    # 获取测试数据
    logger.info("加载测试数据...")
    _, _, test_loader = get_data_loaders(config)

    # 分析模型参数
    param_analysis = analyze_model_parameters(model)

    # MAC分析
    input_shape = (2, sequence_length)
    mac_analysis = comprehensive_mac_analysis(model, input_shape, device)

    # 快速准确率测试
    logger.info("进行快速准确率测试...")
    accuracy = quick_accuracy_test(model, test_loader, device)

    # 汇总结果
    results = {
        'timestamp': datetime.now().isoformat(),
        'model_path': model_path,
        'config_path': config_path,
        'dataset_type': config['data']['dataset_type'],
        'input_shape': input_shape,
        'device': str(device),
        'accuracy': accuracy,
        'parameter_analysis': param_analysis,
        'mac_analysis': mac_analysis,
        'library_availability': {
            'fvcore': FVCORE_AVAILABLE,
            'thop': THOP_AVAILABLE,
            'ptflops': PTFLOPS_AVAILABLE
        }
    }

    # 保存结果
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)

    logger.info(f"分析结果已保存到: {output_path}")

    # 打印总结
    logger.info("\n" + "=" * 80)
    logger.info("📊 论文用分析总结")
    logger.info("=" * 80)
    logger.info(f"模型: {model_path}")
    logger.info(f"数据集: {config['data']['dataset_type']}")
    logger.info(f"输入形状: {input_shape}")
    logger.info(f"快速测试准确率: {accuracy:.2f}%")
    logger.info("")
    logger.info("📝 参数统计（论文用）:")
    logger.info(f"  基础模型参数: {param_analysis['base_params']:,}")
    logger.info(f"  动态适配器参数: {param_analysis['adapter_params']:,}")
    logger.info(f"  总参数数量: {param_analysis['total_params']:,}")
    logger.info("")
    logger.info("🔢 MAC计算:")
    if 'fvcore' in mac_analysis and 'macs_str' in mac_analysis['fvcore']:
        logger.info(f"  fvcore MAC: {mac_analysis['fvcore']['macs_str']}")
    if 'thop' in mac_analysis and 'macs_str' in mac_analysis['thop']:
        logger.info(f"  thop MAC: {mac_analysis['thop']['macs_str']}")
    if 'ptflops' in mac_analysis and 'macs_str' in mac_analysis['ptflops']:
        logger.info(f"  ptflops MAC: {mac_analysis['ptflops']['macs_str']}")
    logger.info("")
    logger.info("📋 论文建议:")
    logger.info(f"  - 参数比较使用: {param_analysis['base_params']:,} 参数")
    logger.info("  - 可在文中说明动态适配器用于多尺度处理")
    logger.info("  - MAC计算基于完整模型（含适配器）")
    logger.info("=" * 80)

if __name__ == '__main__':
    main()
