"""
MAMC去噪单元实现

这个模块实现了MAMC模型的去噪单元，包括：
1. BasicBlock - 基础残差块
2. Shrinkage - 软阈值去噪模块
3. DenosingUnit - 完整的去噪单元

主要特点：
- 基于ResNet的残差结构
- 软阈值去噪技术
- 自适应平均池化
- 批归一化和ReLU激活
"""

import torch
import torch.nn as nn


class Shrinkage(nn.Module):
    """
    软阈值去噪模块
    
    实现自适应软阈值去噪，通过学习阈值参数来去除噪声。
    
    Args:
        channel (int): 输入通道数
        gap_size (int): 全局平均池化的输出大小
    """
    
    def __init__(self, channel, gap_size):
        super(Shrinkage, self).__init__()
        self.gap = nn.AdaptiveAvgPool1d(gap_size)
        self.fc = nn.Sequential(
            nn.Linear(channel, channel),
            nn.BatchNorm1d(channel),
            nn.ReLU(inplace=True),
            nn.Linear(channel, channel),
            nn.<PERSON>g<PERSON>id(),
        )

    def forward(self, x):
        """
        前向传播
        
        Args:
            x: 输入张量 [batch_size, channels, length]
            
        Returns:
            去噪后的张量
        """
        x_raw = x  # 保存原始输入
        x = torch.abs(x)  # 计算绝对值
        x_abs = x
        x = self.gap(x)  # 全局平均池化
        x = torch.flatten(x, 1)  # 展平
        average = torch.mean(x, dim=1, keepdim=True)  # 计算平均值
        x = self.fc(x)  # 通过全连接层学习阈值权重
        x = torch.mul(average, x)  # 加权平均
        
        # 软阈值操作
        x = x.unsqueeze(2)  # 恢复维度
        sub = x_abs - x  # 计算差值
        zeros = sub - sub  # 创建零张量
        n_sub = torch.max(sub, zeros)  # 软阈值操作
        x = torch.mul(torch.sign(x_raw), n_sub)  # 保持原始符号
        
        return x


class BasicBlock(nn.Module):
    """
    基础残差块
    
    实现带有Shrinkage模块的残差块，用于特征提取和去噪。
    
    Args:
        in_channels (int): 输入通道数
        out_channels (int): 输出通道数
        stride (int): 卷积步长，默认为1
    """
    
    expansion = 1
    
    def __init__(self, in_channels, out_channels, stride=1):
        super().__init__()
        self.shrinkage = Shrinkage(out_channels, gap_size=1)
        
        # 残差函数
        self.residual_function = nn.Sequential(
            nn.Conv1d(in_channels, out_channels, kernel_size=3, stride=stride, padding=1, bias=False),
            nn.BatchNorm1d(out_channels),
            nn.ReLU(inplace=True),
            nn.Conv1d(out_channels, out_channels * BasicBlock.expansion, kernel_size=3, padding=1, bias=False),
            nn.BatchNorm1d(out_channels * BasicBlock.expansion),
            self.shrinkage
        )
        
        # 跳跃连接
        self.shortcut = nn.Sequential()
        
        if stride != 1 or in_channels != BasicBlock.expansion * out_channels:
            self.shortcut = nn.Sequential(
                nn.Conv1d(in_channels, out_channels * BasicBlock.expansion, kernel_size=1, stride=stride, bias=False),
                nn.BatchNorm1d(out_channels * BasicBlock.expansion)
            )
            
    def forward(self, x):
        """
        前向传播
        
        Args:
            x: 输入张量
            
        Returns:
            残差块输出
        """
        return nn.ReLU(inplace=True)(self.residual_function(x) + self.shortcut(x))


class DenosingUnit(nn.Module):
    """
    去噪单元
    
    完整的去噪单元，包含初始卷积层和多个BasicBlock。
    
    Args:
        block: 使用的块类型（通常是BasicBlock）
        num_block (int): 块的数量
        in_channel (int): 输入通道数，默认为2 (I/Q)
        out_channel (int): 输出通道数，默认为16
    """

    def __init__(self, block, num_block, in_channel=2, out_channel=16):
        super().__init__()
        self.in_channel = in_channel
        self.out_channel = out_channel
        
        # 初始卷积层
        self.conv1 = nn.Sequential(
            nn.Conv1d(self.in_channel, self.out_channel, kernel_size=3, padding=1, bias=False),
            nn.BatchNorm1d(self.out_channel),
            nn.ReLU(inplace=True)
        )
        
        # 残差块层
        self.conv2_x = self._make_layer(block, self.out_channel, num_block, 1)

    def _make_layer(self, block, out_channel, num_blocks, stride):
        """
        创建残差层
        
        Args:
            block: 块类型
            out_channel (int): 输出通道数
            num_blocks (int): 块的数量
            stride (int): 步长
            
        Returns:
            nn.Sequential: 残差层
        """
        strides = [stride] + [1] * (num_blocks - 1)
        layers = []
        for stride in strides:
            layers.append(block(out_channel, out_channel, stride))

        return nn.Sequential(*layers)

    def forward(self, x):
        """
        前向传播
        
        Args:
            x: 输入张量 [batch_size, in_channel, length]
            
        Returns:
            去噪后的特征张量 [batch_size, out_channel, length]
        """
        x = self.conv1(x)  # 初始卷积
        x = self.conv2_x(x)  # 残差块处理
        return x


def create_denoising_unit(in_channel=2, out_channel=16, num_blocks=2):
    """
    创建去噪单元的便捷函数
    
    Args:
        in_channel (int): 输入通道数
        out_channel (int): 输出通道数
        num_blocks (int): BasicBlock的数量
        
    Returns:
        DenosingUnit: 配置好的去噪单元
    """
    return DenosingUnit(BasicBlock, num_blocks, in_channel, out_channel)


if __name__ == '__main__':
    # 测试去噪单元
    print("测试MAMC去噪单元...")
    
    # 创建去噪单元
    denoising = create_denoising_unit(in_channel=2, out_channel=16, num_blocks=2)
    
    # 测试输入
    batch_size = 4
    in_channels = 2
    length = 1024
    test_input = torch.randn(batch_size, in_channels, length)
    
    print(f"输入形状: {test_input.shape}")
    
    # 前向传播
    with torch.no_grad():
        output = denoising(test_input)
        print(f"输出形状: {output.shape}")
        print(f"输出范围: [{output.min().item():.4f}, {output.max().item():.4f}]")
    
    # 计算参数数量
    total_params = sum(p.numel() for p in denoising.parameters())
    trainable_params = sum(p.numel() for p in denoising.parameters() if p.requires_grad)
    
    print(f"总参数数: {total_params}")
    print(f"可训练参数数: {trainable_params}")
    
    print("去噪单元测试完成！")
