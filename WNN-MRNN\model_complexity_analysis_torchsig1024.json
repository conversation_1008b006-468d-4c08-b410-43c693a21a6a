{"timestamp": "2025-08-23T21:10:41.597188", "model_path": "./saved_models/wnn_mrnn/torchsig1024_20250820_142825/models/best_model.pth", "config_path": "config.yaml", "dataset_type": "torchsig1024", "input_shape": [2, 1024], "device": "cuda", "accuracy": 59.895833333333336, "parameter_analysis": {"base_params": 499929, "adapter_params": 393984, "total_params": 893913, "trainable_params": 893913, "non_trainable_params": 0, "trainable_ratio": 100.0, "param_details": {"conv1.weight": {"shape": [64, 1, 2, 7], "parameters": 896, "trainable": true, "is_adapter": false}, "conv1.bias": {"shape": [64], "parameters": 64, "trainable": true, "is_adapter": false}, "bn1.weight": {"shape": [64], "parameters": 64, "trainable": true, "is_adapter": false}, "bn1.bias": {"shape": [64], "parameters": 64, "trainable": true, "is_adapter": false}, "conv2.weight": {"shape": [64, 64, 5], "parameters": 20480, "trainable": true, "is_adapter": false}, "conv2.bias": {"shape": [64], "parameters": 64, "trainable": true, "is_adapter": false}, "bn2.weight": {"shape": [64], "parameters": 64, "trainable": true, "is_adapter": false}, "bn2.bias": {"shape": [64], "parameters": 64, "trainable": true, "is_adapter": false}, "conv3.weight": {"shape": [64, 64, 3], "parameters": 12288, "trainable": true, "is_adapter": false}, "conv3.bias": {"shape": [64], "parameters": 64, "trainable": true, "is_adapter": false}, "bn3.weight": {"shape": [64], "parameters": 64, "trainable": true, "is_adapter": false}, "bn3.bias": {"shape": [64], "parameters": 64, "trainable": true, "is_adapter": false}, "wavelet.predictors.0.lstm.weight_ih_l0": {"shape": [256, 64], "parameters": 16384, "trainable": true, "is_adapter": false}, "wavelet.predictors.0.lstm.weight_hh_l0": {"shape": [256, 64], "parameters": 16384, "trainable": true, "is_adapter": false}, "wavelet.predictors.0.lstm.bias_ih_l0": {"shape": [256], "parameters": 256, "trainable": true, "is_adapter": false}, "wavelet.predictors.0.lstm.bias_hh_l0": {"shape": [256], "parameters": 256, "trainable": true, "is_adapter": false}, "wavelet.predictors.0.lstm.weight_ih_l0_reverse": {"shape": [256, 64], "parameters": 16384, "trainable": true, "is_adapter": false}, "wavelet.predictors.0.lstm.weight_hh_l0_reverse": {"shape": [256, 64], "parameters": 16384, "trainable": true, "is_adapter": false}, "wavelet.predictors.0.lstm.bias_ih_l0_reverse": {"shape": [256], "parameters": 256, "trainable": true, "is_adapter": false}, "wavelet.predictors.0.lstm.bias_hh_l0_reverse": {"shape": [256], "parameters": 256, "trainable": true, "is_adapter": false}, "wavelet.predictors.0.linear.weight": {"shape": [64, 128], "parameters": 8192, "trainable": true, "is_adapter": false}, "wavelet.predictors.0.linear.bias": {"shape": [64], "parameters": 64, "trainable": true, "is_adapter": false}, "wavelet.predictors.1.lstm.weight_ih_l0": {"shape": [256, 64], "parameters": 16384, "trainable": true, "is_adapter": false}, "wavelet.predictors.1.lstm.weight_hh_l0": {"shape": [256, 64], "parameters": 16384, "trainable": true, "is_adapter": false}, "wavelet.predictors.1.lstm.bias_ih_l0": {"shape": [256], "parameters": 256, "trainable": true, "is_adapter": false}, "wavelet.predictors.1.lstm.bias_hh_l0": {"shape": [256], "parameters": 256, "trainable": true, "is_adapter": false}, "wavelet.predictors.1.lstm.weight_ih_l0_reverse": {"shape": [256, 64], "parameters": 16384, "trainable": true, "is_adapter": false}, "wavelet.predictors.1.lstm.weight_hh_l0_reverse": {"shape": [256, 64], "parameters": 16384, "trainable": true, "is_adapter": false}, "wavelet.predictors.1.lstm.bias_ih_l0_reverse": {"shape": [256], "parameters": 256, "trainable": true, "is_adapter": false}, "wavelet.predictors.1.lstm.bias_hh_l0_reverse": {"shape": [256], "parameters": 256, "trainable": true, "is_adapter": false}, "wavelet.predictors.1.linear.weight": {"shape": [64, 128], "parameters": 8192, "trainable": true, "is_adapter": false}, "wavelet.predictors.1.linear.bias": {"shape": [64], "parameters": 64, "trainable": true, "is_adapter": false}, "wavelet.updaters.0.lstm.weight_ih_l0": {"shape": [256, 64], "parameters": 16384, "trainable": true, "is_adapter": false}, "wavelet.updaters.0.lstm.weight_hh_l0": {"shape": [256, 64], "parameters": 16384, "trainable": true, "is_adapter": false}, "wavelet.updaters.0.lstm.bias_ih_l0": {"shape": [256], "parameters": 256, "trainable": true, "is_adapter": false}, "wavelet.updaters.0.lstm.bias_hh_l0": {"shape": [256], "parameters": 256, "trainable": true, "is_adapter": false}, "wavelet.updaters.0.lstm.weight_ih_l0_reverse": {"shape": [256, 64], "parameters": 16384, "trainable": true, "is_adapter": false}, "wavelet.updaters.0.lstm.weight_hh_l0_reverse": {"shape": [256, 64], "parameters": 16384, "trainable": true, "is_adapter": false}, "wavelet.updaters.0.lstm.bias_ih_l0_reverse": {"shape": [256], "parameters": 256, "trainable": true, "is_adapter": false}, "wavelet.updaters.0.lstm.bias_hh_l0_reverse": {"shape": [256], "parameters": 256, "trainable": true, "is_adapter": false}, "wavelet.updaters.0.linear.weight": {"shape": [64, 128], "parameters": 8192, "trainable": true, "is_adapter": false}, "wavelet.updaters.0.linear.bias": {"shape": [64], "parameters": 64, "trainable": true, "is_adapter": false}, "wavelet.updaters.1.lstm.weight_ih_l0": {"shape": [256, 64], "parameters": 16384, "trainable": true, "is_adapter": false}, "wavelet.updaters.1.lstm.weight_hh_l0": {"shape": [256, 64], "parameters": 16384, "trainable": true, "is_adapter": false}, "wavelet.updaters.1.lstm.bias_ih_l0": {"shape": [256], "parameters": 256, "trainable": true, "is_adapter": false}, "wavelet.updaters.1.lstm.bias_hh_l0": {"shape": [256], "parameters": 256, "trainable": true, "is_adapter": false}, "wavelet.updaters.1.lstm.weight_ih_l0_reverse": {"shape": [256, 64], "parameters": 16384, "trainable": true, "is_adapter": false}, "wavelet.updaters.1.lstm.weight_hh_l0_reverse": {"shape": [256, 64], "parameters": 16384, "trainable": true, "is_adapter": false}, "wavelet.updaters.1.lstm.bias_ih_l0_reverse": {"shape": [256], "parameters": 256, "trainable": true, "is_adapter": false}, "wavelet.updaters.1.lstm.bias_hh_l0_reverse": {"shape": [256], "parameters": 256, "trainable": true, "is_adapter": false}, "wavelet.updaters.1.linear.weight": {"shape": [64, 128], "parameters": 8192, "trainable": true, "is_adapter": false}, "wavelet.updaters.1.linear.bias": {"shape": [64], "parameters": 64, "trainable": true, "is_adapter": false}, "conv_low.0.weight": {"shape": [64, 64, 3], "parameters": 12288, "trainable": true, "is_adapter": false}, "conv_low.0.bias": {"shape": [64], "parameters": 64, "trainable": true, "is_adapter": false}, "conv_low.1.weight": {"shape": [64], "parameters": 64, "trainable": true, "is_adapter": false}, "conv_low.1.bias": {"shape": [64], "parameters": 64, "trainable": true, "is_adapter": false}, "conv_high_list.0.0.weight": {"shape": [64, 64, 3], "parameters": 12288, "trainable": true, "is_adapter": false}, "conv_high_list.0.0.bias": {"shape": [64], "parameters": 64, "trainable": true, "is_adapter": false}, "conv_high_list.0.1.weight": {"shape": [64], "parameters": 64, "trainable": true, "is_adapter": false}, "conv_high_list.0.1.bias": {"shape": [64], "parameters": 64, "trainable": true, "is_adapter": false}, "conv_high_list.1.0.weight": {"shape": [64, 64, 3], "parameters": 12288, "trainable": true, "is_adapter": false}, "conv_high_list.1.0.bias": {"shape": [64], "parameters": 64, "trainable": true, "is_adapter": false}, "conv_high_list.1.1.weight": {"shape": [64], "parameters": 64, "trainable": true, "is_adapter": false}, "conv_high_list.1.1.bias": {"shape": [64], "parameters": 64, "trainable": true, "is_adapter": false}, "mmrnn_classifier.input_proj.weight": {"shape": [64, 64], "parameters": 4096, "trainable": true, "is_adapter": false}, "mmrnn_classifier.input_proj.bias": {"shape": [64], "parameters": 64, "trainable": true, "is_adapter": false}, "mmrnn_classifier.rnn_layers.0.MSBs.0.ln_1.weight": {"shape": [64], "parameters": 64, "trainable": true, "is_adapter": false}, "mmrnn_classifier.rnn_layers.0.MSBs.0.ln_1.bias": {"shape": [64], "parameters": 64, "trainable": true, "is_adapter": false}, "mmrnn_classifier.rnn_layers.0.MSBs.0.self_attention.A_log": {"shape": [128, 16], "parameters": 2048, "trainable": true, "is_adapter": false}, "mmrnn_classifier.rnn_layers.0.MSBs.0.self_attention.D": {"shape": [128], "parameters": 128, "trainable": true, "is_adapter": false}, "mmrnn_classifier.rnn_layers.0.MSBs.0.self_attention.in_proj.weight": {"shape": [256, 64], "parameters": 16384, "trainable": true, "is_adapter": false}, "mmrnn_classifier.rnn_layers.0.MSBs.0.self_attention.conv1d.weight": {"shape": [128, 1, 4], "parameters": 512, "trainable": true, "is_adapter": false}, "mmrnn_classifier.rnn_layers.0.MSBs.0.self_attention.conv1d.bias": {"shape": [128], "parameters": 128, "trainable": true, "is_adapter": false}, "mmrnn_classifier.rnn_layers.0.MSBs.0.self_attention.x_proj.weight": {"shape": [36, 128], "parameters": 4608, "trainable": true, "is_adapter": false}, "mmrnn_classifier.rnn_layers.0.MSBs.0.self_attention.dt_proj.weight": {"shape": [128, 4], "parameters": 512, "trainable": true, "is_adapter": false}, "mmrnn_classifier.rnn_layers.0.MSBs.0.self_attention.dt_proj.bias": {"shape": [128], "parameters": 128, "trainable": true, "is_adapter": false}, "mmrnn_classifier.rnn_layers.0.MSBs.0.self_attention.out_proj.weight": {"shape": [64, 128], "parameters": 8192, "trainable": true, "is_adapter": false}, "mmrnn_classifier.rnn_layers.0.MSBs.0.linear.weight": {"shape": [64, 128], "parameters": 8192, "trainable": true, "is_adapter": false}, "mmrnn_classifier.rnn_layers.0.MSBs.0.linear.bias": {"shape": [64], "parameters": 64, "trainable": true, "is_adapter": false}, "mmrnn_classifier.rnn_layers.0.hidden_state_adapters.512_to_256.weight": {"shape": [256, 512], "parameters": 131072, "trainable": true, "is_adapter": true}, "mmrnn_classifier.rnn_layers.0.hidden_state_adapters.512_to_256.bias": {"shape": [256], "parameters": 256, "trainable": true, "is_adapter": true}, "mmrnn_classifier.rnn_layers.1.MSBs.0.ln_1.weight": {"shape": [64], "parameters": 64, "trainable": true, "is_adapter": false}, "mmrnn_classifier.rnn_layers.1.MSBs.0.ln_1.bias": {"shape": [64], "parameters": 64, "trainable": true, "is_adapter": false}, "mmrnn_classifier.rnn_layers.1.MSBs.0.self_attention.A_log": {"shape": [128, 16], "parameters": 2048, "trainable": true, "is_adapter": false}, "mmrnn_classifier.rnn_layers.1.MSBs.0.self_attention.D": {"shape": [128], "parameters": 128, "trainable": true, "is_adapter": false}, "mmrnn_classifier.rnn_layers.1.MSBs.0.self_attention.in_proj.weight": {"shape": [256, 64], "parameters": 16384, "trainable": true, "is_adapter": false}, "mmrnn_classifier.rnn_layers.1.MSBs.0.self_attention.conv1d.weight": {"shape": [128, 1, 4], "parameters": 512, "trainable": true, "is_adapter": false}, "mmrnn_classifier.rnn_layers.1.MSBs.0.self_attention.conv1d.bias": {"shape": [128], "parameters": 128, "trainable": true, "is_adapter": false}, "mmrnn_classifier.rnn_layers.1.MSBs.0.self_attention.x_proj.weight": {"shape": [36, 128], "parameters": 4608, "trainable": true, "is_adapter": false}, "mmrnn_classifier.rnn_layers.1.MSBs.0.self_attention.dt_proj.weight": {"shape": [128, 4], "parameters": 512, "trainable": true, "is_adapter": false}, "mmrnn_classifier.rnn_layers.1.MSBs.0.self_attention.dt_proj.bias": {"shape": [128], "parameters": 128, "trainable": true, "is_adapter": false}, "mmrnn_classifier.rnn_layers.1.MSBs.0.self_attention.out_proj.weight": {"shape": [64, 128], "parameters": 8192, "trainable": true, "is_adapter": false}, "mmrnn_classifier.rnn_layers.1.MSBs.0.linear.weight": {"shape": [64, 128], "parameters": 8192, "trainable": true, "is_adapter": false}, "mmrnn_classifier.rnn_layers.1.MSBs.0.linear.bias": {"shape": [64], "parameters": 64, "trainable": true, "is_adapter": false}, "mmrnn_classifier.rnn_layers.1.hidden_state_adapters.512_to_256.weight": {"shape": [256, 512], "parameters": 131072, "trainable": true, "is_adapter": true}, "mmrnn_classifier.rnn_layers.1.hidden_state_adapters.512_to_256.bias": {"shape": [256], "parameters": 256, "trainable": true, "is_adapter": true}, "mmrnn_classifier.rnn_layers.2.MSBs.0.ln_1.weight": {"shape": [64], "parameters": 64, "trainable": true, "is_adapter": false}, "mmrnn_classifier.rnn_layers.2.MSBs.0.ln_1.bias": {"shape": [64], "parameters": 64, "trainable": true, "is_adapter": false}, "mmrnn_classifier.rnn_layers.2.MSBs.0.self_attention.A_log": {"shape": [128, 16], "parameters": 2048, "trainable": true, "is_adapter": false}, "mmrnn_classifier.rnn_layers.2.MSBs.0.self_attention.D": {"shape": [128], "parameters": 128, "trainable": true, "is_adapter": false}, "mmrnn_classifier.rnn_layers.2.MSBs.0.self_attention.in_proj.weight": {"shape": [256, 64], "parameters": 16384, "trainable": true, "is_adapter": false}, "mmrnn_classifier.rnn_layers.2.MSBs.0.self_attention.conv1d.weight": {"shape": [128, 1, 4], "parameters": 512, "trainable": true, "is_adapter": false}, "mmrnn_classifier.rnn_layers.2.MSBs.0.self_attention.conv1d.bias": {"shape": [128], "parameters": 128, "trainable": true, "is_adapter": false}, "mmrnn_classifier.rnn_layers.2.MSBs.0.self_attention.x_proj.weight": {"shape": [36, 128], "parameters": 4608, "trainable": true, "is_adapter": false}, "mmrnn_classifier.rnn_layers.2.MSBs.0.self_attention.dt_proj.weight": {"shape": [128, 4], "parameters": 512, "trainable": true, "is_adapter": false}, "mmrnn_classifier.rnn_layers.2.MSBs.0.self_attention.dt_proj.bias": {"shape": [128], "parameters": 128, "trainable": true, "is_adapter": false}, "mmrnn_classifier.rnn_layers.2.MSBs.0.self_attention.out_proj.weight": {"shape": [64, 128], "parameters": 8192, "trainable": true, "is_adapter": false}, "mmrnn_classifier.rnn_layers.2.MSBs.0.linear.weight": {"shape": [64, 128], "parameters": 8192, "trainable": true, "is_adapter": false}, "mmrnn_classifier.rnn_layers.2.MSBs.0.linear.bias": {"shape": [64], "parameters": 64, "trainable": true, "is_adapter": false}, "mmrnn_classifier.rnn_layers.2.hidden_state_adapters.512_to_256.weight": {"shape": [256, 512], "parameters": 131072, "trainable": true, "is_adapter": true}, "mmrnn_classifier.rnn_layers.2.hidden_state_adapters.512_to_256.bias": {"shape": [256], "parameters": 256, "trainable": true, "is_adapter": true}, "mmrnn_classifier.classifier.0.weight": {"shape": [64], "parameters": 64, "trainable": true, "is_adapter": false}, "mmrnn_classifier.classifier.0.bias": {"shape": [64], "parameters": 64, "trainable": true, "is_adapter": false}, "mmrnn_classifier.classifier.1.weight": {"shape": [25, 64], "parameters": 1600, "trainable": true, "is_adapter": false}, "mmrnn_classifier.classifier.1.bias": {"shape": [25], "parameters": 25, "trainable": true, "is_adapter": false}}}, "mac_analysis": {"fvcore": {"macs_raw": 0.19215347200000002, "macs_str": "0", "details": {"conv": 0.047054848, "batch_norm": 0.000524288, "linear": 0.092276288, "layer_norm": 0.0019664, "matmul": 0.050331648}}, "thop": {"macs_raw": 245761856.0, "macs_str": "245.762M", "params_raw": 795993.0, "params_str": "795.993K"}, "ptflops": {"macs_str": "ptflops与Mamba不兼容", "params_str": "ptflops与Mamba不兼容"}}, "library_availability": {"fvcore": true, "thop": true, "ptflops": true}}