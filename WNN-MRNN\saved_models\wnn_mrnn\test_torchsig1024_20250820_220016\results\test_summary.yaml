dataset_info:
  dataset_type: torchsig1024
  input_shape: !!python/tuple
  - 2
  - 1024
  num_classes: 25
  snr_range:
  - 0.0
  - 30.0
  total_samples: 208000
inference_performance:
  avg_inference_time_ms: 0.10490134473030384
  max_inference_time_ms: 0.468691810965538
  min_inference_time_ms: 0.07087364792823792
  std_inference_time_ms: 0.027568008388490783
model_complexity:
  macs: 245.762M
  macs_raw: 245761856.0
  parameters: 795.993K
  params_raw: 795993.0
overall_metrics:
  accuracy: 58.33125
  kappa: 0.5659505208333333
  macro_f1: 56.05867802019837
test_info:
  config_path: config.yaml
  model_path: ./saved_models/wnn_mrnn/torchsig1024_20250820_142825/models/best_model.pth
  test_date: '2025-08-20 22:01:42'
