dataset_info:
  dataset_type: torchsig2048
  input_shape: !!python/tuple
  - 2
  - 2048
  num_classes: 25
  snr_range:
  - 0.0
  - 30.0
  total_samples: 208000
inference_performance:
  avg_inference_time_ms: 0.21884294083485237
  max_inference_time_ms: 0.5608163774013519
  min_inference_time_ms: 0.21509267389774323
  std_inference_time_ms: 0.009409520500601945
model_complexity:
  macs: 2.248G
  macs_raw: 2248114560.0
  parameters: 950.489K
  params_raw: 950489.0
overall_metrics:
  accuracy: 62.98605769230769
  kappa: 0.6144381009615385
  macro_f1: 62.052638156909914
test_info:
  config_path: config.yaml
  model_path: ./saved_models/cldnn/torchsig2048_20250709_143947/models/best_model.pth
  test_date: '2025-08-21 22:12:18'
