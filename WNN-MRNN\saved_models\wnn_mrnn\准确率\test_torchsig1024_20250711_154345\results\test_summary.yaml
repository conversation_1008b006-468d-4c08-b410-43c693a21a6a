dataset_info:
  dataset_type: torchsig1024
  input_shape: !!python/tuple
  - 2
  - 1024
  num_classes: 25
  snr_range:
  - 0.0
  - 30.0
  total_samples: 208000
inference_performance:
  avg_inference_time_ms: 0.10076100665789385
  max_inference_time_ms: 0.623796135187149
  min_inference_time_ms: 0.07674843072891235
  std_inference_time_ms: 0.027031823381638367
model_complexity:
  macs: 687.742M
  macs_raw: 687742208.0
  parameters: 759.769K
  params_raw: 759769.0
overall_metrics:
  accuracy: 60.97788461538462
  kappa: 0.5935196314102564
  macro_f1: 60.03769081236929
test_info:
  config_path: config.yaml
  model_path: ./saved_models/wnn_mrnn/torchsig1024_20250709_190959/models/best_model.pth
  test_date: '2025-07-11 15:46:07'
