#!/usr/bin/env python3
"""
MAWDN测试脚本

这个脚本用于测试训练好的MAWDN模型
使用方法：python test.py --config config.yml --model_path saved_models/best_model.pth
"""

import os
import sys
import argparse
import yaml
import numpy as np
import torch
import torch.nn as nn
import matplotlib.pyplot as plt
from sklearn.metrics import confusion_matrix, classification_report, accuracy_score, f1_score, cohen_kappa_score
import seaborn as sns
import time
import json
from tqdm import tqdm
import logging
from datetime import datetime
from models import MAWDN

# 尝试导入计算MACs的库
try:
    from thop import profile, clever_format
    THOP_AVAILABLE = True
except ImportError:
    try:
        from ptflops import get_model_complexity_info
        PTFLOPS_AVAILABLE = True
        THOP_AVAILABLE = False
    except ImportError:
        THOP_AVAILABLE = False
        PTFLOPS_AVAILABLE = False
        print("警告: 未安装thop或ptflops库，无法计算MACs。请运行: pip install thop 或 pip install ptflops")

# 导入数据加载器
from utils import (
    RML2016Dataset,
    HisarModDataset,
    load_rml_dataset,
    split_dataset,
    get_hisar_data_loaders,
    get_torchsig_data_loaders,
    get_rml201801a_data_loaders
)

def setup_logging(output_dir):
    """设置日志"""
    os.makedirs(output_dir, exist_ok=True)
    log_file = os.path.join(output_dir, f'test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler(sys.stdout)
        ]
    )
    return logging.getLogger(__name__)

def load_config(config_path):
    """加载配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config

def get_data_loaders(config):
    """根据配置获取数据加载器"""
    dataset_type = config['data']['dataset_type']

    if dataset_type == 'rml':
        return get_rml_data_loaders(config)
    elif dataset_type == 'rml201801a':
        return get_rml201801a_data_loaders(config)
    elif dataset_type == 'hisar':
        return get_hisar_data_loaders(config)
    elif dataset_type.startswith('torchsig'):
        return get_torchsig_data_loaders(config)
    else:
        raise ValueError(f"不支持的数据集类型: {dataset_type}")

def get_rml_data_loaders(config):
    """获取RML数据集的数据加载器"""
    # 获取RML配置
    rml_config = config['data']
    file_path = rml_config['rml_file_path']

    # 获取调制类型
    modulations = rml_config.get('modulations')
    # 如果未指定调制类型，使用默认的11种调制类型
    if modulations is None:
        modulations = ['8PSK', 'AM-DSB', 'AM-SSB', 'BPSK', 'CPFSK', 'GFSK', 'PAM4', 'QAM16', 'QAM64', 'QPSK', 'WBFM']

    # 加载数据
    X, labels, snrs = load_rml_dataset(
        file_path=file_path,
        modulations=modulations,
        samples_per_key=rml_config.get('samples_per_key')
    )

    # 分割数据集
    (X_train, y_train, snr_train), (X_test, y_test, snr_test) = split_dataset(
        X, labels, snrs,
        train_ratio=rml_config['train_ratio'],
        seed=config['training']['seed'],
        stratify_by_snr=rml_config['stratify_by_snr']
    )

    # 创建测试数据集
    test_dataset = RML2016Dataset(X_test, y_test, snr_test)

    # 创建数据加载器
    batch_size = config['training']['batch_size']
    test_loader = torch.utils.data.DataLoader(test_dataset, batch_size=batch_size, shuffle=False)

    return None, None, test_loader

def load_model(model_path, config, device):
    """加载训练好的模型"""
    checkpoint = torch.load(model_path, map_location=device)

    # 从检查点获取配置（如果有的话）
    if 'config' in checkpoint:
        saved_config = checkpoint['config']
        # 使用保存的模型配置
        model_config = saved_config['model']
        dataset_type = saved_config['data']['dataset_type']
    else:
        # 使用当前配置
        model_config = config['model']
        dataset_type = config['data']['dataset_type']

    # 根据数据集类型调整参数
    if dataset_type == 'rml':
        # RML数据集有11种调制类型
        num_classes = 11
    elif dataset_type == 'rml201801a':
        num_classes = len(config['rml201801a_class_names'])
    elif dataset_type == 'hisar':
        num_classes = len(config['hisar_class_names'])
    elif dataset_type.startswith('torchsig'):
        num_classes = len(config['torchsig_class_names'])
    else:
        num_classes = model_config.get('num_classes', 26)

    print(f"加载模型 - 数据集: {dataset_type}, 类别数: {num_classes}")

    # 创建模型
    model = MAWDN(
        in_channels=model_config['in_channels'],
        mid_channels=model_config['mid_channels'],
        out_channels=model_config['feature_channels'],
        num_classes=num_classes,
        levels=model_config['decomposition_levels']
    )

    # 加载权重
    model.load_state_dict(checkpoint['model_state_dict'])
    model = model.to(device)

    return model

def calculate_model_macs(model, input_shape, device):
    """计算模型的MACs"""
    try:
        if THOP_AVAILABLE:
            # 使用thop库
            dummy_input = torch.randn(1, *input_shape).to(device)
            macs, params = profile(model, inputs=(dummy_input,), verbose=False)
            macs_str, params_str = clever_format([macs, params], "%.3f")
            return macs, macs_str, params, params_str
        elif PTFLOPS_AVAILABLE:
            # 使用ptflops库
            macs, params = get_model_complexity_info(
                model, input_shape,
                as_strings=True,
                print_per_layer_stat=False,
                verbose=False
            )
            return None, macs, None, params
        else:
            return None, "N/A (请安装thop或ptflops)", None, "N/A"
    except Exception as e:
        return None, f"计算失败: {str(e)}", None, "N/A"

def evaluate_snr_performance(test_results_path, output_dir):
    """按SNR评估性能并绘制曲线"""
    try:
        results = np.load(test_results_path)
        snr_correct = results['snr_correct'].item()
        snr_total = results['snr_total'].item()
        
        # 计算每个SNR值的准确率
        snr_values = sorted(snr_total.keys())
        accuracies = []
        
        for snr in snr_values:
            accuracy = 100 * snr_correct[snr] / snr_total[snr]
            accuracies.append(accuracy)
            print(f"SNR {snr} dB: 准确率 {accuracy:.2f}% ({snr_correct[snr]}/{snr_total[snr]})")
        
        # 绘制SNR-准确率曲线
        plt.figure(figsize=(10, 6))
        plt.plot(snr_values, accuracies, 'o-', linewidth=2)
        plt.grid(True)
        plt.xlabel('SNR (dB)')
        plt.ylabel('Accuracy (%)')
        plt.title('Classification Accuracy vs. SNR')
        plt.xticks(snr_values)
        plt.savefig(os.path.join(output_dir, 'snr_performance.png'), dpi=300, bbox_inches='tight')
        plt.close()
        
        # 保存数据
        np.savez(os.path.join(output_dir, 'snr_performance.npz'),
                 snr_values=snr_values,
                 accuracies=accuracies)
        
        return snr_values, accuracies
    
    except Exception as e:
        print(f"SNR性能评估失败: {e}")
        return None, None

def analyze_results_comprehensive(predictions, targets, snrs, class_names, output_dir, inference_times=None):
    """全面分析测试结果"""
    try:
        # 1. 总体混淆矩阵
        cm = confusion_matrix(targets, predictions)

        plt.figure(figsize=(12, 10))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                    xticklabels=class_names, yticklabels=class_names)
        plt.title('Overall Confusion Matrix')
        plt.xlabel('Predicted')
        plt.ylabel('Actual')
        plt.xticks(rotation=45)
        plt.yticks(rotation=0)
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'confusion_matrix_overall.png'), dpi=300, bbox_inches='tight')
        plt.close()

        # 保存混淆矩阵数据
        cm_data = {
            'confusion_matrix': cm.tolist(),
            'class_names': class_names
        }
        with open(os.path.join(output_dir, 'confusion_matrix_data.json'), 'w') as f:
            json.dump(cm_data, f, indent=2)

        # 2. 分类报告
        report = classification_report(targets, predictions, target_names=class_names, output_dict=True)

        # 保存分类报告
        with open(os.path.join(output_dir, 'classification_report.txt'), 'w') as f:
            f.write(classification_report(targets, predictions, target_names=class_names))

        # 3. 按SNR分析准确率和生成各SNR的混淆矩阵
        if snrs is not None:
            unique_snrs = np.unique(snrs)
            snr_accuracies = []
            snr_f1_scores = []
            snr_kappa_scores = []
            snr_results = {}

            # 创建SNR混淆矩阵目录
            snr_cm_dir = os.path.join(output_dir, 'snr_confusion_matrices')
            os.makedirs(snr_cm_dir, exist_ok=True)

            for snr in unique_snrs:
                mask = snrs == snr
                snr_targets = targets[mask]
                snr_predictions = predictions[mask]

                if len(snr_targets) > 0:
                    snr_acc = np.mean(snr_targets == snr_predictions) * 100
                    snr_f1 = f1_score(snr_targets, snr_predictions, average='macro') * 100
                    snr_kappa = cohen_kappa_score(snr_targets, snr_predictions)

                    snr_accuracies.append(snr_acc)
                    snr_f1_scores.append(snr_f1)
                    snr_kappa_scores.append(snr_kappa)

                    # 保存SNR结果
                    snr_results[f'SNR_{int(snr)}dB'] = {
                        'accuracy': snr_acc,
                        'macro_f1': snr_f1,
                        'kappa': snr_kappa,
                        'sample_count': len(snr_targets)
                    }

                    print(f'SNR {snr:2.0f} dB: Acc={snr_acc:.2f}%, F1={snr_f1:.2f}%, Kappa={snr_kappa:.4f} (n={len(snr_targets)})')

                    # 生成该SNR下的混淆矩阵
                    snr_cm = confusion_matrix(snr_targets, snr_predictions)

                    plt.figure(figsize=(10, 8))
                    sns.heatmap(snr_cm, annot=True, fmt='d', cmap='Blues',
                               xticklabels=class_names, yticklabels=class_names)
                    plt.title(f'Confusion Matrix - SNR {int(snr)} dB\nAcc: {snr_acc:.1f}%, F1: {snr_f1:.1f}%, Kappa: {snr_kappa:.3f}')
                    plt.xlabel('Predicted')
                    plt.ylabel('Actual')
                    plt.xticks(rotation=45)
                    plt.yticks(rotation=0)
                    plt.tight_layout()
                    plt.savefig(os.path.join(snr_cm_dir, f'confusion_matrix_snr_{int(snr)}dB.png'),
                               dpi=300, bbox_inches='tight')
                    plt.close()

            # 保存SNR结果数据
            with open(os.path.join(output_dir, 'snr_results.json'), 'w') as f:
                json.dump(snr_results, f, indent=2)

            # 绘制SNR vs 各种指标曲线
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))

            # 准确率
            axes[0, 0].plot(unique_snrs, snr_accuracies, 'b-o', linewidth=2, markersize=6)
            axes[0, 0].set_xlabel('SNR (dB)')
            axes[0, 0].set_ylabel('Accuracy (%)')
            axes[0, 0].set_title('Accuracy vs SNR')
            axes[0, 0].grid(True, alpha=0.3)

            # Macro-F1
            axes[0, 1].plot(unique_snrs, snr_f1_scores, 'r-o', linewidth=2, markersize=6)
            axes[0, 1].set_xlabel('SNR (dB)')
            axes[0, 1].set_ylabel('Macro-F1 (%)')
            axes[0, 1].set_title('Macro-F1 vs SNR')
            axes[0, 1].grid(True, alpha=0.3)

            # Kappa
            axes[1, 0].plot(unique_snrs, snr_kappa_scores, 'g-o', linewidth=2, markersize=6)
            axes[1, 0].set_xlabel('SNR (dB)')
            axes[1, 0].set_ylabel('Kappa')
            axes[1, 0].set_title('Kappa vs SNR')
            axes[1, 0].grid(True, alpha=0.3)

            # 推理时间分布（如果有的话）
            if inference_times is not None:
                axes[1, 1].hist(inference_times * 1000, bins=50, alpha=0.7, color='purple')
                axes[1, 1].set_xlabel('Inference Time (ms)')
                axes[1, 1].set_ylabel('Frequency')
                axes[1, 1].set_title('Inference Time Distribution')
                axes[1, 1].grid(True, alpha=0.3)
            else:
                axes[1, 1].text(0.5, 0.5, 'No inference time data',
                               ha='center', va='center', transform=axes[1, 1].transAxes)
                axes[1, 1].set_title('Inference Time Distribution')

            plt.tight_layout()
            plt.savefig(os.path.join(output_dir, 'performance_metrics_vs_snr.png'), dpi=300, bbox_inches='tight')
            plt.close()

        # 4. 每个类别的准确率
        class_accuracy = cm.diagonal() / cm.sum(axis=1)

        plt.figure(figsize=(12, 6))
        bars = plt.bar(range(len(class_names)), class_accuracy * 100)
        plt.xlabel('Modulation Type')
        plt.ylabel('Accuracy (%)')
        plt.title('Per-Class Accuracy')
        plt.xticks(range(len(class_names)), class_names, rotation=45)

        # 添加数值标签
        for bar, acc in zip(bars, class_accuracy):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                    f'{acc*100:.1f}%', ha='center', va='bottom')

        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'per_class_accuracy.png'), dpi=300, bbox_inches='tight')
        plt.close()

        return cm, class_accuracy, report

    except Exception as e:
        print(f"结果分析失败: {e}")
        return None, None, None

def find_latest_experiment_dir(base_dir, dataset_type):
    """查找最新的实验目录"""
    if not os.path.exists(base_dir):
        return None

    # 查找匹配的实验目录
    experiment_dirs = []
    for item in os.listdir(base_dir):
        if item.startswith(f"{dataset_type}_") and os.path.isdir(os.path.join(base_dir, item)):
            experiment_dirs.append(item)

    if not experiment_dirs:
        return None

    # 按时间戳排序，返回最新的
    experiment_dirs.sort(reverse=True)
    return os.path.join(base_dir, experiment_dirs[0])

def create_test_output_directories(config, model_path):
    """创建测试输出目录结构"""
    dataset_type = config['data']['dataset_type']
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # 基础输出目录
    base_output_dir = config['output_dir']

    # 创建测试结果目录
    test_dir = os.path.join(base_output_dir, f"test_{dataset_type}_{timestamp}")

    # 创建子目录
    directories = {
        'test': test_dir,
        'results': os.path.join(test_dir, 'results'),
        'plots': os.path.join(test_dir, 'plots'),
        'logs': os.path.join(test_dir, 'logs'),
        'configs': os.path.join(test_dir, 'configs')
    }

    # 创建所有目录
    for dir_path in directories.values():
        os.makedirs(dir_path, exist_ok=True)

    print(f"测试结果目录创建完成: {test_dir}")
    print(f"子目录包括: results, plots, logs, configs")

    return directories

def plot_confusion_matrix(predictions, targets, class_names, output_dir):
    """绘制并保存混淆矩阵（保持向后兼容）"""
    return analyze_results_comprehensive(predictions, targets, None, class_names, output_dir)

def test_model(model, test_loader, device, logger):
    """测试模型"""
    model.eval()
    correct = 0
    total = 0
    all_predictions = []
    all_targets = []
    all_snrs = []
    inference_times = []

    with torch.no_grad():
        pbar = tqdm(test_loader, desc='Testing')
        for data, target, snr in pbar:
            data, target = data.to(device), target.to(device)

            # 测量推理时间
            start_time = time.time()
            output = model(data)
            end_time = time.time()

            # 记录每个样本的推理时间
            batch_inference_time = (end_time - start_time) / data.size(0)  # 每个样本的时间
            inference_times.extend([batch_inference_time] * data.size(0))

            # 处理不同输出格式
            if isinstance(output, list):  # 训练模式下返回所有级别的输出
                # 使用最后一级的输出计算准确率
                pred = output[-1].argmax(dim=1, keepdim=True)
            else:  # 评估模式下仅返回最后一级的输出
                pred = output.argmax(dim=1, keepdim=True)

            correct += pred.eq(target.view_as(pred)).sum().item()
            total += target.size(0)

            # 收集预测结果
            all_predictions.extend(pred.cpu().numpy().flatten())
            all_targets.extend(target.cpu().numpy())
            all_snrs.extend(snr.numpy())

            # 更新进度条
            pbar.set_postfix({'Acc': f'{100.*correct/total:.2f}%'})

    accuracy = 100. * correct / total

    # 计算额外的评估指标
    all_predictions = np.array(all_predictions)
    all_targets = np.array(all_targets)
    all_snrs = np.array(all_snrs)
    inference_times = np.array(inference_times)

    macro_f1 = f1_score(all_targets, all_predictions, average='macro') * 100
    kappa = cohen_kappa_score(all_targets, all_predictions)

    # 推理时间统计
    avg_inference_time = np.mean(inference_times) * 1000  # 转换为毫秒
    std_inference_time = np.std(inference_times) * 1000

    logger.info(f'Test Accuracy: {accuracy:.2f}%')
    logger.info(f'Macro-F1: {macro_f1:.2f}%')
    logger.info(f'Kappa: {kappa:.4f}')
    logger.info(f'平均推理时间: {avg_inference_time:.3f} ± {std_inference_time:.3f} ms/sample')

    return accuracy, macro_f1, kappa, all_predictions, all_targets, all_snrs, inference_times

def main():
    parser = argparse.ArgumentParser(description='MAWDN测试脚本')
    parser.add_argument('--config', type=str, default='config.yaml', help='配置文件路径')
    parser.add_argument('--model_path', type=str, default=None, help='模型文件路径')
    parser.add_argument('--output_dir', type=str, default=None, help='结果输出目录')
    parser.add_argument('--auto_find', action='store_true', help='自动查找最新的训练模型')
    args = parser.parse_args()

    # 加载配置
    config = load_config(args.config)
    dataset_type = config['data']['dataset_type']

    # 自动查找模型路径
    if args.model_path is None or args.auto_find:
        base_dir = config['output_dir']
        latest_exp_dir = find_latest_experiment_dir(base_dir, dataset_type)

        if latest_exp_dir:
            auto_model_path = os.path.join(latest_exp_dir, 'models', 'best_model.pth')
            if os.path.exists(auto_model_path):
                args.model_path = auto_model_path
                print(f"自动找到模型: {auto_model_path}")
            else:
                print(f"警告: 在 {latest_exp_dir} 中未找到 best_model.pth")

        if args.model_path is None:
            # 使用默认路径
            args.model_path = 'saved_models/mawdn/best_model.pth'

    # 检查模型文件是否存在
    if not os.path.exists(args.model_path):
        print(f"错误: 模型文件不存在: {args.model_path}")
        print("请先训练模型或指定正确的模型路径:")
        print("  python test.py --model_path your_model_path.pth")
        print("或使用 --auto_find 参数自动查找最新模型:")
        print("  python test.py --auto_find")
        return

    # 创建测试输出目录结构
    if args.output_dir is None:
        directories = create_test_output_directories(config, args.model_path)
        output_dir = directories['results']
    else:
        output_dir = args.output_dir
        os.makedirs(output_dir, exist_ok=True)
        directories = {'results': output_dir, 'logs': output_dir, 'configs': output_dir, 'plots': output_dir}

    # 设置日志（使用logs目录）
    logger = setup_logging(directories['logs'])
    logger.info(f"开始测试，配置文件: {args.config}")
    logger.info(f"模型文件: {args.model_path}")
    logger.info(f"数据集类型: {config['data']['dataset_type']}")
    logger.info(f"测试结果目录: {directories.get('test', output_dir)}")

    # 保存配置文件副本到configs目录
    config_backup_path = os.path.join(directories['configs'], 'test_config_backup.yaml')
    with open(config_backup_path, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
    logger.info(f"配置文件备份保存到: {config_backup_path}")

    # 设置设备
    device = torch.device(config['training']['device'] if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")

    # 获取测试数据加载器
    logger.info("加载测试数据...")
    _, _, test_loader = get_data_loaders(config)

    # 加载模型
    logger.info("加载模型...")
    model = load_model(args.model_path, config, device)

    # 计算模型MACs
    logger.info("计算模型复杂度...")
    dataset_type = config['data']['dataset_type']
    if dataset_type == 'rml':
        input_shape = (2, config['data']['sequence_lengths']['rml'])
    elif dataset_type == 'hisar':
        input_shape = (2, config['data']['sequence_lengths']['hisar'])
    elif dataset_type.startswith('torchsig'):
        input_shape = (2, config['data']['sequence_lengths'][dataset_type])
    else:
        input_shape = (2, 1024)  # 默认值

    macs_raw, macs_str, params_raw, params_str = calculate_model_macs(model, input_shape, device)
    logger.info(f"模型MACs: {macs_str}")
    logger.info(f"模型参数: {params_str}")

    # 测试模型
    logger.info("开始测试...")
    accuracy, macro_f1, kappa, predictions, targets, snrs, inference_times = test_model(model, test_loader, device, logger)

    # 分析结果（使用plots目录保存图片，results目录保存数据）
    logger.info("分析结果...")

    # 获取类别名称
    if dataset_type == 'rml':
        # RML数据集的11种调制类型
        class_names = ['8PSK', 'AM-DSB', 'AM-SSB', 'BPSK', 'CPFSK', 'GFSK', 'PAM4', 'QAM16', 'QAM64', 'QPSK', 'WBFM']
    elif dataset_type == 'rml201801a':
        class_names = config['rml201801a_class_names']
    elif dataset_type == 'hisar':
        class_names = config['hisar_class_names']
    elif dataset_type.startswith('torchsig'):
        class_names = config['torchsig_class_names']
    else:
        class_names = [f'Class_{i}' for i in range(len(np.unique(targets)))]

    report = analyze_results_comprehensive(predictions, targets, snrs, class_names, directories['plots'], inference_times)

    # 保存详细的结果摘要
    summary = {
        'overall_metrics': {
            'accuracy': accuracy,
            'macro_f1': macro_f1,
            'kappa': kappa
        },
        'model_complexity': {
            'macs': macs_str,
            'parameters': params_str,
            'macs_raw': macs_raw if macs_raw is not None else 'N/A',
            'params_raw': params_raw if params_raw is not None else 'N/A'
        },
        'inference_performance': {
            'avg_inference_time_ms': float(np.mean(inference_times) * 1000) if inference_times is not None else 'N/A',
            'std_inference_time_ms': float(np.std(inference_times) * 1000) if inference_times is not None else 'N/A',
            'min_inference_time_ms': float(np.min(inference_times) * 1000) if inference_times is not None else 'N/A',
            'max_inference_time_ms': float(np.max(inference_times) * 1000) if inference_times is not None else 'N/A'
        },
        'dataset_info': {
            'total_samples': len(targets),
            'dataset_type': config['data']['dataset_type'],
            'input_shape': input_shape,
            'num_classes': len(np.unique(targets)),
            'snr_range': [float(np.min(snrs)), float(np.max(snrs))]
        },
        'test_info': {
            'model_path': args.model_path,
            'config_path': args.config,
            'test_date': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
    }

    # 保存为YAML和JSON格式到results目录
    with open(os.path.join(directories['results'], 'test_summary.yaml'), 'w') as f:
        yaml.dump(summary, f, default_flow_style=False)

    with open(os.path.join(directories['results'], 'test_summary.json'), 'w') as f:
        json.dump(summary, f, indent=2)

    # 保存推理时间数据
    if inference_times is not None:
        inference_data = {
            'inference_times_ms': (inference_times * 1000).tolist(),
            'statistics': {
                'mean': float(np.mean(inference_times) * 1000),
                'std': float(np.std(inference_times) * 1000),
                'min': float(np.min(inference_times) * 1000),
                'max': float(np.max(inference_times) * 1000),
                'median': float(np.median(inference_times) * 1000)
            }
        }
        with open(os.path.join(directories['results'], 'inference_times.json'), 'w') as f:
            json.dump(inference_data, f, indent=2)

    logger.info(f"测试完成！")
    logger.info(f"总体准确率: {accuracy:.2f}%")
    logger.info(f"Macro-F1: {macro_f1:.2f}%")
    logger.info(f"Kappa: {kappa:.4f}")
    logger.info(f"模型MACs: {macs_str}")
    if inference_times is not None:
        logger.info(f"平均推理时间: {np.mean(inference_times)*1000:.3f} ms/sample")
    logger.info(f"测试结果保存在: {directories.get('test', output_dir)}")
    logger.info(f"生成的文件包括:")
    logger.info(f"  - 图片文件: {directories['plots']}/")
    logger.info(f"    * 总体混淆矩阵: confusion_matrix_overall.png")
    logger.info(f"    * 各SNR混淆矩阵: snr_confusion_matrices/")
    logger.info(f"    * 性能指标图: performance_metrics_vs_snr.png")
    logger.info(f"  - 数据文件: {directories['results']}/")
    logger.info(f"    * 详细结果: test_summary.json, snr_results.json")
    logger.info(f"    * 推理时间: inference_times.json")
    logger.info(f"  - 日志文件: {directories['logs']}/")
    logger.info(f"  - 配置备份: {directories['configs']}/")
        
if __name__ == '__main__':
    main() 