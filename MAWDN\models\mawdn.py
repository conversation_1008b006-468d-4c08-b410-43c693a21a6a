import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np

class SELayer(nn.Module):
    """Squeeze-and-Excitation模块"""
    def __init__(self, channel, reduction=16):
        super(SELayer, self).__init__()
        self.avg_pool = nn.AdaptiveAvgPool1d(1)
        self.fc = nn.Sequential(
            nn.Linear(channel, channel // reduction, bias=False),
            nn.ReLU(inplace=True),
            nn.Linear(channel // reduction, channel, bias=False),
            nn.Sigmoid()
        )

    def forward(self, x):
        b, c, _ = x.size()
        y = self.avg_pool(x).view(b, c)
        y = self.fc(y).view(b, c, 1)
        return x * y.expand_as(x)

class Bneck(nn.Module):
    """带有SE机制的瓶颈模块，用于Updater和Predictor"""
    def __init__(self, in_channels, kernel_size=3, use_se=True):
        super(Bneck, self).__init__()
        expansion = 3  # 扩展系数
        mid_channels = in_channels * expansion
        
        self.conv1 = nn.Conv1d(in_channels, mid_channels, kernel_size=1, bias=False)
        self.bn1 = nn.BatchNorm1d(mid_channels)
        self.relu = nn.ReLU(inplace=True)
        
        self.dwconv = nn.Conv1d(mid_channels, mid_channels, kernel_size=kernel_size, 
                               padding=kernel_size//2, groups=mid_channels, bias=False)
        self.bn2 = nn.BatchNorm1d(mid_channels)
        
        self.use_se = use_se
        if use_se:
            self.se = SELayer(mid_channels, reduction=16)
            
        self.conv2 = nn.Conv1d(mid_channels, in_channels, kernel_size=1, bias=False)
        self.bn3 = nn.BatchNorm1d(in_channels)
        
        # 残差连接
        self.shortcut = nn.Identity()

    def forward(self, x):
        identity = self.shortcut(x)
        
        out = self.conv1(x)
        out = self.bn1(out)
        out = self.relu(out)
        
        out = self.dwconv(out)
        out = self.bn2(out)
        out = self.relu(out)
        
        if self.use_se:
            out = self.se(out)
            
        out = self.conv2(out)
        out = self.bn3(out)
        
        out += identity
        out = self.relu(out)
        
        return out

class PredictorUpdater(nn.Module):
    """使用神经网络实现的预测器和更新器"""
    def __init__(self, in_channels):
        super(PredictorUpdater, self).__init__()
        
        # 首尾均使用卷积层，中间是4个bneck模块
        self.conv1 = nn.Conv1d(in_channels, in_channels, kernel_size=3, padding=1, bias=False)
        self.bn1 = nn.BatchNorm1d(in_channels)
        self.relu = nn.ReLU(inplace=True)
        
        # 四个bneck模块，其中第一个不使用SE模块
        self.bneck1 = Bneck(in_channels, kernel_size=5, use_se=False)
        self.bneck2 = Bneck(in_channels, kernel_size=3, use_se=True)
        self.bneck3 = Bneck(in_channels, kernel_size=3, use_se=True)
        self.bneck4 = Bneck(in_channels, kernel_size=3, use_se=True)
        
        self.conv2 = nn.Conv1d(in_channels, in_channels, kernel_size=3, padding=1, bias=False)
        self.bn2 = nn.BatchNorm1d(in_channels)

    def forward(self, x):
        x = self.conv1(x)
        x = self.bn1(x)
        x = self.relu(x)
        
        x = self.bneck1(x)
        x = self.bneck2(x)
        x = self.bneck3(x)
        x = self.bneck4(x)
        
        x = self.conv2(x)
        x = self.bn2(x)
        x = self.relu(x)
        
        return x

class LiftingScheme(nn.Module):
    """自适应提升小波变换的基本单元"""
    def __init__(self, in_channels):
        super(LiftingScheme, self).__init__()
        self.predictor = PredictorUpdater(in_channels)
        self.updater = PredictorUpdater(in_channels)
        
    def forward(self, x):
        # 信号分解为奇偶部分 (splitting)
        even_indices = torch.arange(0, x.shape[-1], 2, device=x.device)
        odd_indices = torch.arange(1, x.shape[-1], 2, device=x.device)
        
        even = x[:, :, even_indices]  # 偶数样本
        odd = x[:, :, odd_indices]    # 奇数样本
        
        # 预测步骤: 计算高频部分 (H)
        predicted_odd = self.predictor(even)
        high = odd - predicted_odd  # 高频分量
        
        # 更新步骤: 修改低频部分 (L)
        updated_even = even + self.updater(high)
        low = updated_even  # 低频分量
        
        return low, high

class AttBlock(nn.Module):
    """注意力模块，用于特征加强"""
    def __init__(self, in_channels, out_channels):
        super(AttBlock, self).__init__()
        self.avg_pool = nn.AdaptiveAvgPool1d(1)
        self.concat = nn.Identity()  # 占位，实际操作在forward中进行
        self.se = SELayer(in_channels, reduction=8)
        self.fc = nn.Linear(in_channels, out_channels)
        
    def forward(self, x_list):
        # x_list包含各个分解级别的特征
        # 对每个特征进行全局平均池化
        pooled_features = [self.avg_pool(x).squeeze(-1) for x in x_list]
        
        # 拼接特征
        concat_features = torch.cat(pooled_features, dim=1)
        
        # 应用SE注意力机制
        b, c = concat_features.size()
        se_input = concat_features.view(b, c, 1)
        se_output = self.se(se_input).squeeze(-1)
        
        # 使用全连接层调整通道数
        output = self.fc(se_output)
        
        return output

class Classifier(nn.Module):
    """分类器网络"""
    def __init__(self, in_channels, num_classes):
        super(Classifier, self).__init__()
        self.fc1 = nn.Linear(in_channels, in_channels)
        self.dropout = nn.Dropout(0.5)
        self.relu = nn.ReLU(inplace=True)
        self.fc2 = nn.Linear(in_channels, num_classes)
        self.softmax = nn.Softmax(dim=1)
        
    def forward(self, x):
        x = self.fc1(x)
        x = self.dropout(x)
        x = self.relu(x)
        x = self.fc2(x)
        return x

class MultiChannelFeatureExtraction(nn.Module):
    """多通道特征提取模块 (模块A)"""
    def __init__(self, in_channels=2, out_channels=32):
        super(MultiChannelFeatureExtraction, self).__init__()
        
        # 联合处理I/Q数据的2D卷积分支
        self.conv1 = nn.Conv2d(1, out_channels, kernel_size=(2, 7), padding=(0, 3), bias=False)
        self.bn1 = nn.BatchNorm2d(out_channels)
        self.relu1 = nn.ReLU(inplace=True)
        
        # 分别处理I和Q数据的1D卷积分支
        self.conv2 = nn.Conv1d(1, out_channels, kernel_size=7, padding=3, bias=False)
        self.conv3 = nn.Conv1d(1, out_channels, kernel_size=7, padding=3, bias=False)
        self.bn2 = nn.BatchNorm1d(out_channels)
        self.bn3 = nn.BatchNorm1d(out_channels)
        self.relu2 = nn.ReLU(inplace=True)
        self.relu3 = nn.ReLU(inplace=True)
        
        # 融合不同通道的特征
        self.conv4 = nn.Conv2d(out_channels, out_channels, kernel_size=(1, 7), padding=(0, 3), bias=False)
        self.bn4 = nn.BatchNorm2d(out_channels)
        self.relu4 = nn.ReLU(inplace=True)
        
        # 最终的特征融合 - 修正输入通道数为128 (out_channels + 2*out_channels + out_channels)
        self.conv5 = nn.Conv1d(out_channels*4, out_channels*2, kernel_size=7, padding=3, bias=False)
        self.bn5 = nn.BatchNorm1d(out_channels*2)
        self.relu5 = nn.ReLU(inplace=True)
        
    def forward(self, x):
        batch_size, _, seq_len = x.size()
        
        # 联合处理I/Q路径 (2D卷积)
        x_joint = x.unsqueeze(1)  # [B, 1, 2, L]
        x_joint = self.conv1(x_joint)  # [B, C, 1, L]
        x_joint = self.bn1(x_joint)
        x_joint = self.relu1(x_joint)
        
        # 单独处理I/Q路径 (1D卷积)
        x_i = x[:, 0, :].unsqueeze(1)  # [B, 1, L], I通道
        x_q = x[:, 1, :].unsqueeze(1)  # [B, 1, L], Q通道
        
        x_i = self.conv2(x_i)  # [B, C, L]
        x_i = self.bn2(x_i)
        x_i = self.relu2(x_i)
        
        x_q = self.conv3(x_q)  # [B, C, L]
        x_q = self.bn3(x_q)
        x_q = self.relu3(x_q)
        
        # 融合I/Q通道特征
        x_iq = torch.stack([x_i, x_q], dim=2)  # [B, C, 2, L]
        x_iq = self.conv4(x_iq)  # [B, C, 1, L]
        x_iq = self.bn4(x_iq)
        x_iq = self.relu4(x_iq)
        x_iq = x_iq.squeeze(2)  # [B, C, L]
        
        # 合并两个分支的特征
        x_joint = x_joint.squeeze(2)  # [B, C, L]
        if x_joint.dim() == 4:  # 如果还是4D的形状 [B, C, 1, L]
            x_joint = x_joint.view(batch_size, -1, seq_len)  # 变成 [B, C, L]
        
        if x_iq.dim() == 4:  # 如果是4D的形状 [B, C, 1, L]
            x_iq = x_iq.view(batch_size, -1, seq_len)  # 变成 [B, C, L]
        
        # 移除调试信息
        x_concat = torch.cat([x_joint, x_iq, x_i], dim=1)  # [B, 3C, L]
        # 移除调试信息
        
        # 最终特征提取
        x_out = self.conv5(x_concat)  # [B, 2C, L]
        x_out = self.bn5(x_out)
        x_out = self.relu5(x_out)
        
        return x_out  # [B, 2C, L]

class AdaptiveWaveletDecomposition(nn.Module):
    """自适应小波分解模块 (模块B)"""
    def __init__(self, in_channels, levels=3):
        super(AdaptiveWaveletDecomposition, self).__init__()
        self.levels = levels
        
        # 为每个分解级别创建提升方案
        self.lifting_schemes = nn.ModuleList([
            LiftingScheme(in_channels) for _ in range(levels)
        ])
        
    def forward(self, x):
        # 初始化低频和高频分量列表
        lows = []
        highs = []
        
        # 第一级分解的输入
        current_low = x
        
        # 多级小波分解
        for i in range(self.levels):
            # 通过第i级提升方案
            low, high = self.lifting_schemes[i](current_low)
            
            # 存储结果
            lows.append(low)
            highs.append(high)
            
            # 低频部分作为下一级的输入
            current_low = low
        
        # 返回所有低频和高频分量
        return lows, highs

class ResidualClassificationFlow(nn.Module):
    """残差分类流模块 (模块C)"""
    def __init__(self, in_channels, mid_channels, num_classes, levels=3):
        super(ResidualClassificationFlow, self).__init__()
        self.levels = levels
        
        # 对每个分解级别创建特征提取模块
        self.att_blocks = nn.ModuleList()
        for i in range(levels):
            # 输入通道数是累积的特征数量
            input_channels = in_channels * (i + 2)  # 低频 + 所有高频
            self.att_blocks.append(AttBlock(input_channels, mid_channels))
        
        # 每个级别对应一个分类器
        self.classifiers = nn.ModuleList()
        for _ in range(levels):
            self.classifiers.append(Classifier(mid_channels, num_classes))
    
    def forward(self, lows, highs):
        outputs = []
        features = []
        
        # 对每个级别进行分类
        for i in range(self.levels):
            # 收集当前级别需要的特征
            level_features = [lows[i]] + highs[:i+1]
            
            # 通过注意力模块
            att_output = self.att_blocks[i](level_features)
            
            # 如果不是第一级，加入前一级的特征 (残差连接)
            if i > 0:
                att_output = att_output + features[-1]
            
            # 保存当前特征
            features.append(att_output)
            
            # 通过分类器
            cls_output = self.classifiers[i](att_output)
            outputs.append(cls_output)
        
        return outputs

class MAWDN(nn.Module):
    """多级自适应小波分解网络"""
    def __init__(self, in_channels=2, mid_channels=64, out_channels=32, num_classes=26, levels=3):
        super(MAWDN, self).__init__()
        
        # 模块A: 多通道特征提取
        self.feature_extraction = MultiChannelFeatureExtraction(in_channels, out_channels)
        
        # 模块B: 自适应小波分解
        self.wavelet_decomp = AdaptiveWaveletDecomposition(out_channels*2, levels=levels)
        
        # 模块C: 残差分类流
        self.classification_flow = ResidualClassificationFlow(out_channels*2, mid_channels, num_classes, levels=levels)
        
        self.levels = levels
        
    def forward(self, x):
        # 模块A: 特征提取
        features = self.feature_extraction(x)  # [B, 2C, L]
        
        # 模块B: 小波分解
        lows, highs = self.wavelet_decomp(features)  # 返回各级别的低频和高频分量
        
        # 模块C: 分类
        outputs = self.classification_flow(lows, highs)
        
        # 返回最后一个级别的输出作为最终结果
        # 训练时返回所有级别的输出
        if self.training:
            return outputs
        else:
            # 确保non-training模式下返回单个tensor
            return outputs[-1]
    
    def _compute_loss(self, outputs, targets, gamma=0.9):
        """
        计算多级别分类的加权损失
        
        Args:
            outputs: 模型输出的分类结果列表，每个级别一个
            targets: 真实标签
            gamma: 各级别损失的权重因子
        
        Returns:
            总损失
        """
        criterion = nn.CrossEntropyLoss()
        
        # 计算各级别的分类损失
        classification_loss = 0
        for i, output in enumerate(outputs):
            # 根据级别应用不同的权重，越深的级别权重越大
            weight = gamma ** (self.levels - i - 1)
            classification_loss += weight * criterion(output, targets)
        
        # 小波分解的正则化损失可以在这里添加
        total_loss = classification_loss
        
        return total_loss 