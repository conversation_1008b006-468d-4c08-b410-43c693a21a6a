dataset_info:
  dataset_type: torchsig1024
  input_shape: !!python/tuple
  - 2
  - 1024
  num_classes: 25
  snr_range:
  - 0.0
  - 30.0
  total_samples: 208000
inference_performance:
  avg_inference_time_ms: 0.046957869942371666
  max_inference_time_ms: 0.650063157081604
  min_inference_time_ms: 0.018982216715812683
  std_inference_time_ms: 0.018420936308162515
model_complexity:
  macs: 4.117M
  macs_raw: 4117120.0
  parameters: 415.257K
  params_raw: 415257.0
overall_metrics:
  accuracy: 51.61634615384615
  kappa: 0.49600360576923075
  macro_f1: 50.104121537224486
test_info:
  config_path: config.yaml
  model_path: saved_models/mamc/torchsig1024_20250820_113738/models/best_model.pth
  test_date: '2025-08-20 17:17:07'
