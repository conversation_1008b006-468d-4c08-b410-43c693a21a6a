import os
import argparse
import yaml
import time
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.optim.lr_scheduler import ReduceLROnPlateau
import matplotlib.pyplot as plt
import json
from tqdm import tqdm
from sklearn.metrics import f1_score, cohen_kappa_score
from datetime import datetime
import logging
import sys

from models import MAWDN
from utils import (
    RML2016Dataset,
    HisarModDataset,
    load_rml_dataset,
    split_dataset,
    get_hisar_data_loaders,
    get_torchsig_data_loaders,  # 使用新的通用加载函数
    get_rml201801a_data_loaders
)

def setup_logging(output_dir):
    """设置日志"""
    os.makedirs(output_dir, exist_ok=True)
    log_file = os.path.join(output_dir, f'train_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler(sys.stdout)
        ]
    )
    return logging.getLogger(__name__)

def create_output_directories(config):
    """创建包含数据集和时间信息的输出目录结构"""
    dataset_type = config['data']['dataset_type']
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # 基础输出目录
    base_output_dir = config['output_dir']

    # 创建细分的目录结构
    experiment_dir = os.path.join(base_output_dir, f"{dataset_type}_{timestamp}")

    # 创建子目录
    directories = {
        'experiment': experiment_dir,
        'models': os.path.join(experiment_dir, 'models'),
        'logs': os.path.join(experiment_dir, 'logs'),
        'results': os.path.join(experiment_dir, 'results'),
        'configs': os.path.join(experiment_dir, 'configs'),
        'plots': os.path.join(experiment_dir, 'plots')
    }

    # 创建所有目录
    for dir_path in directories.values():
        os.makedirs(dir_path, exist_ok=True)

    # 更新配置中的输出目录
    config['output_dir'] = experiment_dir
    config['directories'] = directories

    print(f"实验目录创建完成: {experiment_dir}")
    print(f"子目录包括: models, logs, results, configs, plots")

    return directories

def load_config(config_path):
    """加载YAML配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        try:
            config = yaml.safe_load(f)
            return config
        except yaml.YAMLError as e:
            print(f"配置文件加载错误: {e}")
            exit(1)

def count_trainable_parameters(model):
    """计算模型的可训练参数数量"""
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    total_params = sum(p.numel() for p in model.parameters())
    return trainable_params, total_params

def build_model(config):
    """
    构建模型
    
    Args:
        config: 配置字
    
    Returns:
        model: 构建的模型
    """
    model_config = config['model']
    
    in_channels = model_config.get('in_channels', 2)
    out_channels = model_config.get('feature_channels', 32)
    mid_channels = model_config.get('mid_channels', 64)
    num_classes = model_config.get('num_classes', 26)
    levels = model_config.get('decomposition_levels', 3)
    
    model = MAWDN(
        in_channels=in_channels,
        mid_channels=mid_channels,
        out_channels=out_channels,
        num_classes=num_classes,
        levels=levels
    )
    
    return model

def get_data_loaders(config):
    """
    获取数据加载器

    Args:
        config: 配置字典

    Returns:
        train_loader, val_loader, test_loader: 数据加载器
    """
    dataset_type = config['data'].get('dataset_type', 'rml')

    # 设置随机种子
    seed = config['training'].get('seed', 42)
    torch.manual_seed(seed)
    np.random.seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)

    print(f"数据集类型: {dataset_type}")

    # 获取数据集特定的SNR范围
    snr_ranges = config['data'].get('snr_ranges', {})
    dataset_snr_range = None

    if dataset_type in snr_ranges:
        dataset_snr_range = snr_ranges[dataset_type]
        print(f"使用{dataset_type}数据集的特定SNR范围: {dataset_snr_range}")
    else:
        # 如果没有特定配置，使用默认配置
        default_snr_range = config['data'].get('snr_range', [-20, 18])
        dataset_snr_range = default_snr_range
        print(f"无特定SNR范围配置，使用默认范围: {dataset_snr_range}")

    # 更新配置中的SNR范围
    config['data']['snr_range'] = dataset_snr_range

    # 设置数据集特定的序列长度（如果有）
    sequence_lengths = config['data'].get('sequence_lengths', {})
    if dataset_type in sequence_lengths:
        seq_length = sequence_lengths[dataset_type]
        print(f"使用{dataset_type}数据集的特定序列长度: {seq_length}")
        config['model']['sequence_length'] = seq_length

    if dataset_type == 'rml':
        # RML数据集加载
        file_path = config['data'].get('rml_file_path', 'data/RML2016.10a_dict.pkl')
        modulations = config['data'].get('modulations')
        # 如果未指定调制类型，使用默认的11种调制类型
        if modulations is None:
            modulations = ['8PSK', 'AM-DSB', 'AM-SSB', 'BPSK', 'CPFSK', 'GFSK', 'PAM4', 'QAM16', 'QAM64', 'QPSK', 'WBFM']
        snr_range = config['data'].get('snr_range')
        samples_per_key = config['data'].get('samples_per_key')

        # 加载数据集
        print(f"从 {file_path} 加载RML数据集...")
        X, labels, snrs = load_rml_dataset(file_path, modulations, samples_per_key)

        # 过滤SNR
        if snr_range:
            snr_min, snr_max = snr_range
            print(f"过滤SNR范围: {snr_min} dB到 {snr_max} dB")
            snr_mask = (snrs >= snr_min) & (snrs <= snr_max)
            X = X[snr_mask]
            labels = labels[snr_mask]
            snrs = snrs[snr_mask]
            print(f"过滤后样本数量: {len(X)}")

        # 数据集划分 - 首先分割训练集和测试集
        print(f"使用训练比例 {config['data'].get('train_ratio', 0.7)} 分割数据集...")
        stratify_by_snr = config['data'].get('stratify_by_snr', False)
        train_data, test_data = split_dataset(
            X, labels, snrs,
            train_ratio=config['data'].get('train_ratio', 0.7),
            seed=seed,
            stratify_by_snr=stratify_by_snr
        )

        X_train, y_train, snrs_train = train_data
        X_test, y_test, snrs_test = test_data

        # 使用分层采样进一步分割训练集为训练和验证
        val_ratio = config['data'].get('val_ratio', 0.3)

        def create_stratified_train_val_split(X, y, snr, val_ratio, seed):
            """创建分层采样的训练和验证集划分"""
            np.random.seed(seed)

            # 获取唯一的SNR和标签值
            unique_snrs = np.unique(snr)
            unique_labels = np.unique(y)

            train_indices = []
            val_indices = []

            # 对每个SNR和标签组合进行分层采样
            for snr_val in unique_snrs:
                for label_val in unique_labels:
                    # 找到符合条件的索引
                    mask = (snr == snr_val) & (y == label_val)
                    indices = np.where(mask)[0]

                    if len(indices) == 0:
                        continue  # 跳过没有样本的组合

                    # 随机打乱
                    np.random.shuffle(indices)

                    # 分割
                    n_val = int(len(indices) * val_ratio)
                    val_indices.extend(indices[:n_val])
                    train_indices.extend(indices[n_val:])

            # 再次打乱
            np.random.shuffle(train_indices)
            np.random.shuffle(val_indices)

            return train_indices, val_indices

        # 创建分层采样的训练和验证索引
        train_indices, val_indices = create_stratified_train_val_split(
            X_train, y_train, snrs_train, val_ratio, seed
        )

        # 根据索引分割数据
        X_train_final = X_train[train_indices]
        y_train_final = y_train[train_indices]
        snrs_train_final = snrs_train[train_indices]

        X_val = X_train[val_indices]
        y_val = y_train[val_indices]
        snrs_val = snrs_train[val_indices]

        print(f"RML数据集划分完成:")
        print(f"  训练集: {len(X_train_final)} 样本")
        print(f"  验证集: {len(X_val)} 样本")
        print(f"  测试集: {len(X_test)} 样本")

        # 验证分层采样效果
        print(f"训练集SNR分布: {np.unique(snrs_train_final, return_counts=True)[1][:5]}...")
        print(f"验证集SNR分布: {np.unique(snrs_val, return_counts=True)[1][:5]}...")

        # 创建数据集
        batch_size = config['training'].get('batch_size', 64)
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

        train_dataset = RML2016Dataset(X_train_final, y_train_final, snrs_train_final, device='cpu')
        val_dataset = RML2016Dataset(X_val, y_val, snrs_val, device='cpu')
        test_dataset = RML2016Dataset(X_test, y_test, snrs_test, device='cpu')

        # 创建数据加载器
        num_workers = config['training'].get('num_workers', 4)
        pin_memory = config['training'].get('pin_memory', True)
        persistent_workers = config['training'].get('persistent_workers', False) and num_workers > 0

        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True,
                                 num_workers=num_workers, pin_memory=pin_memory,
                                 persistent_workers=persistent_workers)
        val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False,
                               num_workers=num_workers, pin_memory=pin_memory,
                               persistent_workers=persistent_workers)
        test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False,
                                num_workers=num_workers, pin_memory=pin_memory,
                                persistent_workers=persistent_workers)

        print(f"数据加载完成: 训练集 {len(train_dataset)} 样本, 验证集 {len(val_dataset)} 样本, 测试集 {len(test_dataset)} 样本")

    elif dataset_type == 'rml201801a':
        # 使用RML2018.01a数据加载器
        num_workers = config['training'].get('num_workers', 4)
        pin_memory = config['training'].get('pin_memory', True)
        persistent_workers = config['training'].get('persistent_workers', False) and num_workers > 0

        train_loader, val_loader, test_loader = get_rml201801a_data_loaders(
            config,
            num_workers=num_workers,
            pin_memory=pin_memory,
            persist_workers=persistent_workers
        )

    elif dataset_type == 'hisar':
        # 使用改进的HisarMod数据加载器
        num_workers = config['training'].get('num_workers', 4)
        pin_memory = config['training'].get('pin_memory', True)
        persistent_workers = config['training'].get('persistent_workers', False) and num_workers > 0
        
        train_loader, val_loader, test_loader = get_hisar_data_loaders(
            config, 
            num_workers=num_workers,
            pin_memory=pin_memory,
            persist_workers=persistent_workers
        )
    
    elif dataset_type.startswith('torchsig'):
        # 使用通用TorchSig数据加载器处理所有TorchSig数据集
        num_workers = config['training'].get('num_workers', 4)
        pin_memory = config['training'].get('pin_memory', True)
        persistent_workers = config['training'].get('persistent_workers', False) and num_workers > 0
        
        train_loader, val_loader, test_loader = get_torchsig_data_loaders(
            config, 
            num_workers=num_workers,
            pin_memory=pin_memory,
            persist_workers=persistent_workers
        )
        
    else:
        raise ValueError(f"不支持的数据集类型: {dataset_type}")
    
    return train_loader, val_loader, test_loader

def train_model(model, train_loader, val_loader, config):
    """
    训练模型
    
    Args:
        model: 模型
        train_loader: 训练数据加载器
        val_loader: 验证数据加载器
        config: 配置字典
    
    Returns:
        model: 训练好的模型
    """
    device = model.parameters().__next__().device
    
    # 获取训练参数
    training_config = config['training']
    num_epochs = training_config.get('epochs', 100)
    initial_lr = training_config.get('learning_rate', 0.001)
    weight_decay = training_config.get('weight_decay', 1e-5)
    patience = training_config.get('patience', 10)
    
    # 定义优化器和学习率调度器
    optimizer = optim.Adam(model.parameters(), lr=initial_lr, weight_decay=weight_decay)
    scheduler = ReduceLROnPlateau(optimizer, mode='min', factor=0.5, patience=patience, verbose=True)
    
    # 训练循环
    best_val_loss = float('inf')
    best_model_state = None
    early_stop_counter = 0
    early_stop_patience = training_config.get('early_stop_patience', 20)
    
    # 创建保存模型的目录
    save_dir = config.get('output_dir', 'saved_models')
    os.makedirs(save_dir, exist_ok=True)
    
    # 记录训练历史
    history = {
        'train_loss': [],
        'val_loss': [],
        'train_acc': [],
        'val_acc': []
    }
    
    print(f"开始训练，共{num_epochs}个Epoch")
    
    for epoch in range(num_epochs):
        start_time = time.time()
        
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_correct = 0
        train_total = 0
        
        for batch_idx, (data, target, _) in enumerate(train_loader):
            optimizer.zero_grad()
            
            # 前向传播
            outputs = model(data)
            
            # 处理不同输出格式
            if isinstance(outputs, list):  # 训练模式下返回所有级别的输出
                # 使用模型内部的损失计算方法处理多级别输出
                loss = model._compute_loss(outputs, target)
                # 使用最后一级的输出计算准确率
                _, predicted = torch.max(outputs[-1].data, 1)
            else:  # 评估模式下仅返回最后一级的输出
                loss = model._compute_loss(outputs, target)
                _, predicted = torch.max(outputs.data, 1)
            
            # 反向传播
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
            
            # 计算准确率（使用最后一级的输出）
            train_total += target.size(0)
            train_correct += (predicted == target).sum().item()
            
            # 打印批次进度
            if (batch_idx + 1) % 20 == 0:
                print(f"Epoch [{epoch+1}/{num_epochs}], Batch [{batch_idx+1}/{len(train_loader)}], Loss: {loss.item():.4f}")
        
        # 计算训练集平均损失和准确率
        train_loss /= len(train_loader)
        train_acc = 100 * train_correct / train_total
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        val_correct = 0
        val_total = 0
        
        with torch.no_grad():
            for data, target, _ in val_loader:
                # 前向传播
                outputs = model(data)
                
                # 处理不同输出格式
                if isinstance(outputs, list):  # 训练模式下返回所有级别的输出
                    # 使用模型内部的损失计算方法处理多级别输出
                    loss = model._compute_loss(outputs, target)
                    # 使用最后一级的输出计算准确率
                    _, predicted = torch.max(outputs[-1].data, 1)
                else:  # 评估模式下仅返回最后一级的输出
                    loss = model._compute_loss(outputs, target)
                    _, predicted = torch.max(outputs.data, 1)
                
                val_loss += loss.item()
                
                # 计算准确率
                val_total += target.size(0)
                val_correct += (predicted == target).sum().item()
        
        # 计算验证集平均损失和准确率
        val_loss /= len(val_loader)
        val_acc = 100 * val_correct / val_total
        
        # 更新学习率
        scheduler.step(val_loss)
        
        # 记录历史
        history['train_loss'].append(train_loss)
        history['val_loss'].append(val_loss)
        history['train_acc'].append(train_acc)
        history['val_acc'].append(val_acc)
        
        epoch_time = time.time() - start_time
        
        # 打印结果
        print(f"Epoch [{epoch+1}/{num_epochs}], Time: {epoch_time:.2f}s, Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.2f}%, Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.2f}%")
        
        # 保存最佳模型
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            best_model_state = model.state_dict()
            early_stop_counter = 0
            
            # 保存最佳模型
            best_val_acc_str = f"{val_acc:.2f}".replace('.', '_')
            best_model_path = os.path.join(save_dir, f'model_best_acc{best_val_acc_str}.pth')
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_loss': val_loss,
                'val_accuracy': val_acc,
                'config': config
            }, best_model_path)
            
            print(f"模型已保存! (验证损失: {val_loss:.4f})")
        else:
            early_stop_counter += 1
            if early_stop_counter >= early_stop_patience:
                print(f"提前停止训练! {early_stop_patience}个Epoch内验证损失未改善")
                break
        
        # 保存最近的模型
        if (epoch + 1) % 10 == 0:
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'train_loss': train_loss,
                'val_loss': val_loss,
                'config': config
            }, os.path.join(save_dir, f'mawdn_epoch_{epoch+1}.pth'))
    
    # 保存训练历史
    np.save(os.path.join(save_dir, 'training_history.npy'), history)
    
    # 加载最佳模型
    if best_model_state is not None:
        model.load_state_dict(best_model_state)
    
    return model

def evaluate_model(model, test_loader, config):
    """
    评估模型
    
    Args:
        model: 模型
        test_loader: 测试数据加载器
        config: 配置字典
    """
    device = model.parameters().__next__().device
    model.eval()
    
    test_loss = 0.0
    test_correct = 0
    test_total = 0
    
    # 获取数据集特定的SNR范围
    dataset_type = config['data'].get('dataset_type', 'rml')
    snr_ranges = config['data'].get('snr_ranges', {})
    
    if dataset_type in snr_ranges:
        snr_min, snr_max = snr_ranges[dataset_type]
    else:
        snr_min, snr_max = config['data'].get('snr_range', [-20, 18])
    
    print(f"评估使用{dataset_type}数据集的SNR范围: {snr_min}dB 到 {snr_max}dB")
    
    # 用于按SNR统计准确率
    snr_correct = {}
    snr_total = {}
    
    # 用于统计混淆矩阵
    all_preds = []
    all_targets = []
    
    with torch.no_grad():
        for data, target, snr in test_loader:
            # 前向传播
            outputs = model(data)
            
            # 处理不同输出格式
            if isinstance(outputs, list):  # 训练模式下返回所有级别的输出
                # 使用模型内部的损失计算方法处理多级别输出
                loss = model._compute_loss(outputs, target)
                # 使用最后一级的输出计算准确率
                _, predicted = torch.max(outputs[-1].data, 1)
            else:  # 评估模式下仅返回最后一级的输出
                loss = model._compute_loss(outputs, target)
                _, predicted = torch.max(outputs.data, 1)
            
            test_loss += loss.item()
            
            # 统计混淆矩阵数据
            all_preds.extend(predicted.cpu().numpy())
            all_targets.extend(target.cpu().numpy())
            
            # 按SNR统计
            for i in range(len(snr)):
                snr_val = snr[i].item()
                if snr_val not in snr_correct:
                    snr_correct[snr_val] = 0
                    snr_total[snr_val] = 0
                
                snr_total[snr_val] += 1
                if predicted[i] == target[i]:
                    snr_correct[snr_val] += 1
    
    # 计算总体性能指标
    test_loss /= len(test_loader)
    test_acc = 100 * test_correct / test_total
    
    print(f"\n测试结果:")
    print(f"测试损失: {test_loss:.4f}")
    print(f"测试准确率: {test_acc:.2f}%")
    
    # 打印按SNR的准确率
    print("\n按SNR的准确率:")
    for snr_val in sorted(snr_total.keys()):
        snr_acc = 100 * snr_correct[snr_val] / snr_total[snr_val]
        print(f"SNR {snr_val} dB: {snr_acc:.2f}% ({snr_correct[snr_val]}/{snr_total[snr_val]})")
    
    # 保存测试结果
    save_dir = config.get('output_dir', 'saved_models')
    os.makedirs(save_dir, exist_ok=True)
    
    # 保存测试性能指标
    np.savez(os.path.join(save_dir, 'test_results.npz'),
             test_loss=test_loss,
             test_acc=test_acc,
             snr_correct=snr_correct,
             snr_total=snr_total,
             predictions=all_preds,
             targets=all_targets,
             dataset_type=dataset_type,
             snr_range=[snr_min, snr_max])  # 保存数据集类型和SNR范围

def main():
    """
    主函数：加载配置、准备数据、训练和评估模型
    """
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='MAWDN 模型训练')
    parser.add_argument('--config', type=str, default='config.yaml', help='配置文件路径')
    parser.add_argument('--dataset_type', type=str, default=None,
                       choices=['rml', 'hisar', 'torchsig1024', 'torchsig2048', 'torchsig4096'],
                        help='数据集类型，覆盖配置文件中的设置')
    parser.add_argument('--batch_size', type=int, default=None, help='批次大小，覆盖配置文件中的设置')
    parser.add_argument('--epochs', type=int, default=None, help='训练轮数，覆盖配置文件中的设置')
    parser.add_argument('--output_dir', type=str, default=None, help='输出目录，覆盖配置文件中的设置')
    parser.add_argument('--gpu', type=int, default=0, help='使用的GPU索引')
    args = parser.parse_args()

    # 如果没有提供任何命令行参数，直接使用默认配置
    if len(sys.argv) == 1:
        print("使用默认配置文件: config.yaml")
        args.config = 'config.yaml'

    # 加载配置
    config = load_config(args.config)

    # 创建输出目录结构
    directories = create_output_directories(config)

    # 设置日志（使用新的logs目录）
    logger = setup_logging(directories['logs'])
    logger.info(f"开始训练，配置文件: {args.config}")
    logger.info(f"数据集类型: {config['data']['dataset_type']}")
    logger.info(f"实验目录: {directories['experiment']}")

    # 保存配置文件副本到configs目录
    config_backup_path = os.path.join(directories['configs'], 'config_backup.yaml')
    with open(config_backup_path, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
    logger.info(f"配置文件备份保存到: {config_backup_path}")

    # 覆盖配置文件中的设置
    if args.dataset_type is not None:
        config['data']['dataset_type'] = args.dataset_type
        logger.info(f"使用命令行指定的数据集类型: {args.dataset_type}")

    if args.batch_size is not None:
        config['training']['batch_size'] = args.batch_size

    if args.epochs is not None:
        config['training']['epochs'] = args.epochs

    if args.output_dir is not None:
        config['output_dir'] = args.output_dir

    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")

    # 设置随机种子
    torch.manual_seed(config['training']['seed'])
    np.random.seed(config['training']['seed'])
    
    # 获取数据集特定的SNR范围和序列长度
    dataset_type = config['data']['dataset_type']

    # 应用数据集特定的SNR范围
    snr_ranges = config['data'].get('snr_ranges', {})
    if dataset_type in snr_ranges:
        config['data']['snr_range'] = snr_ranges[dataset_type]
        logger.info(f"使用{dataset_type}数据集的特定SNR范围: {config['data']['snr_range']}")

    # 应用数据集特定的序列长度
    sequence_lengths = config['data'].get('sequence_lengths', {})
    if dataset_type in sequence_lengths:
        original_length = config['model']['sequence_length']
        config['model']['sequence_length'] = sequence_lengths[dataset_type]
        if original_length != config['model']['sequence_length']:
            logger.info(f"已根据数据集类型自动调整序列长度: {original_length} -> {config['model']['sequence_length']}")

    # 根据数据集类型自动设置num_classes
    dataset_classes = {
        'hisar': 26,       # Hisar数据集: 26种调制类型
        'rml': 11,         # RML数据集: 11种调制类型
        'rml201801a': 24   # RML2018.01a数据集: 24种调制类型
    }

    # 所有torchsig数据集都是25种调制类型
    if dataset_type.startswith('torchsig'):
        dataset_classes[dataset_type] = 25

    # 自动更新num_classes
    if dataset_type in dataset_classes:
        original_classes = config['model']['num_classes']
        config['model']['num_classes'] = dataset_classes[dataset_type]
        if original_classes != config['model']['num_classes']:
            logger.info(f"已根据数据集类型自动调整num_classes: {original_classes} -> {config['model']['num_classes']}")

    # 准备数据加载器
    logger.info("加载数据...")
    train_loader, val_loader, test_loader = get_data_loaders(config)

    # 创建模型
    logger.info("创建模型...")
    model = build_model(config)
    model = model.to(device)

    # 打印可训练参数数量
    trainable_params, total_params = count_trainable_parameters(model)
    logger.info(f"可训练参数数量: {trainable_params:,}")
    logger.info(f"总参数数量: {total_params:,}")
    logger.info(f"可训练参数比例: {trainable_params/total_params*100:.2f}%")
    
    # 设置优化器和学习率调度器
    learning_rate = float(config['training']['learning_rate'])
    weight_decay = float(config['training']['weight_decay'])
    optimizer = optim.Adam(model.parameters(), lr=learning_rate, weight_decay=weight_decay)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='max', factor=0.5, patience=config['training']['patience'], verbose=True
    )
    
    # 设置损失函数
    criterion = nn.CrossEntropyLoss()
    
    # 训练设置
    num_epochs = config['training']['epochs']
    
    # 使用新的目录结构
    save_dir = directories['models']
    logger.info(f"模型将保存到: {save_dir}")
    
    # 初始化最佳性能跟踪
    best_val_acc = 0.0
    best_epoch = 0
    
    # 初始化早停设置
    early_stopping = config['training'].get('early_stopping', False)
    early_stop_patience = config['training'].get('early_stop_patience', 20)
    min_delta = config['training'].get('min_delta', 0.001)
    monitor = config['training'].get('monitor', 'val_acc')
    patience_counter = 0
    
    # 训练历史记录
    history = {
        'epochs': [],
        'train_loss': [],
        'train_acc': [],
        'val_loss': [],
        'val_acc': [],
        'val_f1': [],
        'val_kappa': [],
        'epoch_time': [],
        'learning_rate': []
    }
    
    # 开始训练循环
    logger.info(f"开始训练，总共 {num_epochs} 轮...")
    for epoch in range(num_epochs):
        epoch_start_time = time.time()
        logger.info(f"Epoch {epoch+1}/{num_epochs}")

        # 训练阶段
        model.train()
        train_loss = 0.0
        train_correct = 0
        train_total = 0

        train_pbar = tqdm(train_loader, desc=f"Epoch {epoch+1}/{num_epochs} [Train]")
        for batch in train_pbar:
            # 处理不同格式的数据批次
            if len(batch) == 3:  # [data, label, snr]
                data, target, _ = batch
            else:  # [data, label]
                data, target = batch

            data, target = data.to(device), target.to(device)

            # 前向传播
            optimizer.zero_grad()
            output = model(data)

            # 处理不同输出格式
            if isinstance(output, list):  # 训练模式下返回所有级别的输出
                # 使用模型内部的损失计算方法处理多级别输出
                loss = model._compute_loss(output, target)
                # 使用最后一级的输出计算准确率
                _, predicted = torch.max(output[-1].data, 1)
            else:  # 评估模式下仅返回最后一级的输出
                loss = criterion(output, target)
                _, predicted = torch.max(output.data, 1)

            # 反向传播
            loss.backward()
            optimizer.step()

            # 统计
            train_loss += loss.item()
            train_total += target.size(0)
            train_correct += (predicted == target).sum().item()

            # 更新进度条
            train_pbar.set_postfix({'loss': f"{loss.item():.4f}"})

        train_loss = train_loss / len(train_loader)
        train_acc = 100. * train_correct / train_total
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        val_correct = 0
        val_total = 0
        all_val_predictions = []
        all_val_targets = []

        with torch.no_grad():
            val_pbar = tqdm(val_loader, desc=f"Epoch {epoch+1}/{num_epochs} [Val]")
            for batch in val_pbar:
                # 处理不同格式的数据批次
                if len(batch) == 3:  # [data, label, snr]
                    data, target, _ = batch
                else:  # [data, label]
                    data, target = batch

                data, target = data.to(device), target.to(device)

                # 前向传播
                output = model(data)

                # 处理不同输出格式
                if isinstance(output, list):  # 训练模式下返回所有级别的输出
                    # 使用模型内部的损失计算方法处理多级别输出
                    loss = model._compute_loss(output, target)
                    # 使用最后一级的输出计算准确率
                    _, predicted = torch.max(output[-1].data, 1)
                else:  # 评估模式下仅返回最后一级的输出
                    loss = criterion(output, target)
                    _, predicted = torch.max(output.data, 1)

                # 统计
                val_loss += loss.item()
                val_total += target.size(0)
                val_correct += (predicted == target).sum().item()

                # 收集预测结果用于计算更多指标
                all_val_predictions.extend(predicted.cpu().numpy())
                all_val_targets.extend(target.cpu().numpy())

                # 更新进度条
                val_pbar.set_postfix({'loss': f"{loss.item():.4f}"})

        val_loss = val_loss / len(val_loader)
        val_acc = 100. * val_correct / val_total

        # 计算额外的评估指标
        all_val_predictions = np.array(all_val_predictions)
        all_val_targets = np.array(all_val_targets)

        val_macro_f1 = f1_score(all_val_targets, all_val_predictions, average='macro') * 100
        val_kappa = cohen_kappa_score(all_val_targets, all_val_predictions)
        
        # 更新学习率
        scheduler.step(val_acc)

        # 记录训练时间
        epoch_time = time.time() - epoch_start_time
        logger.info(f"Epoch {epoch+1} 训练时间: {epoch_time:.2f} 秒")

        # 保存训练历史
        history['epochs'].append(epoch + 1)
        history['train_loss'].append(train_loss)
        history['train_acc'].append(train_acc)
        history['val_loss'].append(val_loss)
        history['val_acc'].append(val_acc)
        history['val_f1'].append(val_macro_f1)
        history['val_kappa'].append(val_kappa)
        history['epoch_time'].append(epoch_time)
        history['learning_rate'].append(optimizer.param_groups[0]['lr'])

        # 打印进度
        logger.info(f"Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.2f}%")
        logger.info(f"Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.2f}%, Macro-F1: {val_macro_f1:.2f}%, Kappa: {val_kappa:.4f}")
        
        # 保存最佳模型
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            best_epoch = epoch + 1

            # 保存最佳模型到models目录
            best_model_path = os.path.join(directories['models'], 'best_model.pth')
            torch.save({
                'epoch': epoch + 1,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'scheduler_state_dict': scheduler.state_dict(),
                'val_acc': val_acc,
                'val_f1': val_macro_f1,
                'val_kappa': val_kappa,
                'config': config,
                'training_history': history
            }, best_model_path)

            # 同时保存一个带时间戳的模型副本
            timestamp_model_path = os.path.join(directories['models'], f'model_epoch_{epoch+1}_acc_{val_acc:.2f}.pth')
            torch.save({
                'epoch': epoch + 1,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'scheduler_state_dict': scheduler.state_dict(),
                'val_acc': val_acc,
                'val_f1': val_macro_f1,
                'val_kappa': val_kappa,
                'config': config,
                'training_history': history
            }, timestamp_model_path)

            logger.info(f"保存最佳模型，验证准确率: {val_acc:.2f}%, Macro-F1: {val_macro_f1:.2f}%, Kappa: {val_kappa:.4f}")
            logger.info(f"模型保存到: {best_model_path}")
            logger.info(f"模型副本保存到: {timestamp_model_path}")

            # 重置早停计数器
            patience_counter = 0
        else:
            patience_counter += 1

        # 每10轮保存一次检查点
        if (epoch + 1) % 10 == 0:
            checkpoint_path = os.path.join(directories['models'], f'checkpoint_epoch_{epoch+1}.pth')
            torch.save({
                'epoch': epoch + 1,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'scheduler_state_dict': scheduler.state_dict(),
                'val_acc': val_acc,
                'config': config
            }, checkpoint_path)
        
        # 早停检查
        if early_stopping and patience_counter >= early_stop_patience:
            logger.info(f"早停触发，最佳验证准确率: {best_val_acc:.2f}%")
            break

    # 保存训练历史到results目录
    history_file = os.path.join(directories['results'], 'training_history.json')
    with open(history_file, 'w') as f:
        json.dump(history, f, indent=2)
    logger.info(f"训练历史保存到: {history_file}")

    # 保存训练摘要信息
    total_training_time = sum(history['epoch_time'])
    avg_epoch_time = total_training_time / len(history['epoch_time'])

    training_summary = {
        'dataset_type': config['data']['dataset_type'],
        'model_type': 'MAWDN',
        'experiment_timestamp': directories['experiment'].split('_')[-1],
        'total_epochs': len(history['epochs']),
        'best_validation_accuracy': float(best_val_acc),
        'best_macro_f1': float(history['val_f1'][history['val_acc'].index(max(history['val_acc']))]),
        'best_kappa': float(history['val_kappa'][history['val_acc'].index(max(history['val_acc']))]),
        'total_training_time_seconds': float(total_training_time),
        'total_training_time_minutes': float(total_training_time / 60),
        'average_epoch_time_seconds': float(avg_epoch_time),
        'final_learning_rate': float(optimizer.param_groups[0]['lr']),
        'trainable_parameters': trainable_params,
        'total_parameters': total_params,
        'config_file': args.config
    }

    summary_file = os.path.join(directories['results'], 'training_summary.json')
    with open(summary_file, 'w') as f:
        json.dump(training_summary, f, indent=2)
    logger.info(f"训练摘要保存到: {summary_file}")
    
    # 绘制训练历史曲线
    plt.figure(figsize=(12, 5))

    plt.subplot(1, 2, 1)
    plt.plot(history['train_loss'], label='Train')
    plt.plot(history['val_loss'], label='Validation')
    plt.title('Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()

    plt.subplot(1, 2, 2)
    plt.plot(history['train_acc'], label='Train')
    plt.plot(history['val_acc'], label='Validation')
    plt.title('Accuracy')
    plt.xlabel('Epoch')
    plt.ylabel('Accuracy (%)')
    plt.legend()

    plt.tight_layout()
    plt.savefig(os.path.join(directories['plots'], 'training_history.png'))

    # 输出最终统计信息
    logger.info("训练完成！")
    logger.info(f"最佳验证准确率: {best_val_acc:.2f}% (Epoch {best_epoch})")
    logger.info(f"总训练时间: {total_training_time:.2f} 秒 ({total_training_time/60:.1f} 分钟)")
    logger.info(f"平均每轮训练时间: {avg_epoch_time:.2f} 秒")
    logger.info(f"实验结果保存在: {directories['experiment']}")
    
    # 在测试集上评估最佳模型
    logger.info("在测试集上评估最佳模型...")
    best_model_path = os.path.join(directories['models'], 'best_model.pth')
    checkpoint = torch.load(best_model_path)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    test_correct = 0
    test_total = 0
    class_correct = [0] * config['model']['num_classes']
    class_total = [0] * config['model']['num_classes']
    
    # 按SNR分组的准确率统计
    snr_correct = {}
    snr_total = {}
    
    with torch.no_grad():
        test_pbar = tqdm(test_loader, desc="Testing")
        for batch in test_pbar:
            if len(batch) == 3:  # [data, label, snr]
                data, target, snrs = batch
            else:  # [data, label]
                data, target = batch
                snrs = None
            
            data, target = data.to(device), target.to(device)
            
            output = model(data)
            
            # 处理不同输出格式
            if isinstance(output, list):  # 训练模式下返回所有级别的输出
                # 使用模型内部的损失计算方法处理多级别输出
                loss = model._compute_loss(output, target)
                # 使用最后一级的输出计算准确率
                _, predicted = torch.max(output[-1].data, 1)
            else:  # 评估模式下仅返回最后一级的输出
                loss = criterion(output, target)
                _, predicted = torch.max(output.data, 1)
            
            # 总体准确率
            test_total += target.size(0)
            test_correct += (predicted == target).sum().item()
            
            # 按类别统计准确率
            for i in range(target.size(0)):
                label = target[i].item()
                class_correct[label] += (predicted[i] == label).item()
                class_total[label] += 1
            
            # 按SNR统计准确率（如果有SNR信息）
            if snrs is not None:
                for i in range(len(snrs)):
                    snr = snrs[i].item()
                    if snr not in snr_correct:
                        snr_correct[snr] = 0
                        snr_total[snr] = 0
                    snr_total[snr] += 1
                    if predicted[i] == target[i]:
                        snr_correct[snr] += 1
    
    # 打印总体准确率
    test_acc = 100. * test_correct / test_total
    logger.info(f"测试集准确率: {test_acc:.2f}%")

    # 打印每个类别的准确率
    # 使用配置文件中的类别名称（如果有）
    if dataset_type == 'rml':
        class_names = config.get('rml_class_names', [f"Class {i}" for i in range(config['model']['num_classes'])])
    elif dataset_type == 'rml201801a':
        class_names = config.get('rml201801a_class_names', [f"Class {i}" for i in range(config['model']['num_classes'])])
    elif dataset_type == 'hisar':
        class_names = config.get('hisar_class_names', [f"Class {i}" for i in range(config['model']['num_classes'])])
    elif dataset_type.startswith('torchsig'):
        class_names = config.get('torchsig_class_names', [f"Class {i}" for i in range(config['model']['num_classes'])])
    else:
        class_names = config.get('class_names', [f"Class {i}" for i in range(config['model']['num_classes'])])

    class_acc_file = os.path.join(directories['results'], 'class_accuracy.csv')
    with open(class_acc_file, 'w') as f:
        f.write("Class,Accuracy,Correct,Total\n")
        for i in range(len(class_correct)):
            if class_total[i] > 0:
                acc = 100. * class_correct[i] / class_total[i]
                logger.info(f"{class_names[i]}: {acc:.2f}% ({class_correct[i]}/{class_total[i]})")
                f.write(f"{class_names[i]},{acc:.4f},{class_correct[i]},{class_total[i]}\n")
    
    # 如果有SNR信息，打印并保存按SNR的准确率
    if snrs is not None and len(snr_correct) > 0:
        snr_acc_file = os.path.join(directories['results'], 'snr_accuracy.csv')
        with open(snr_acc_file, 'w') as f:
            f.write("SNR,Accuracy,Correct,Total\n")
            for snr in sorted(snr_total.keys()):
                acc = 100. * snr_correct[snr] / snr_total[snr]
                logger.info(f"SNR {snr} dB: {acc:.2f}% ({snr_correct[snr]}/{snr_total[snr]})")
                f.write(f"{snr},{acc:.4f},{snr_correct[snr]},{snr_total[snr]}\n")

        # 绘制SNR vs Accuracy曲线
        plt.figure(figsize=(10, 6))
        snrs = sorted(snr_total.keys())
        accs = [100. * snr_correct[snr] / snr_total[snr] for snr in snrs]

        plt.plot(snrs, accs, 'bo-')
        plt.grid(True)
        plt.xlabel('Signal to Noise Ratio (dB)')
        plt.ylabel('Accuracy (%)')

        # 获取数据集特定的SNR范围用于设置x轴
        if dataset_type in config['data'].get('snr_ranges', {}):
            snr_min, snr_max = config['data']['snr_ranges'][dataset_type]
            # 设置x轴范围，稍微扩展一点以便更好地显示
            plt.xlim([snr_min - 2, snr_max + 2])
            plt.title(f'Accuracy vs SNR - {dataset_type} Dataset (Range: {snr_min} to {snr_max} dB)')
        else:
            plt.title(f'Accuracy vs SNR - {dataset_type} Dataset')

        plt.savefig(os.path.join(directories['plots'], 'snr_accuracy.png'))
    
    # 保存测试结果摘要
    summary_file = os.path.join(directories['results'], 'test_summary.txt')
    with open(summary_file, 'w') as f:
        f.write(f"Model: MAWDN\n")
        f.write(f"Dataset: {config['data']['dataset_type']}\n")
        f.write(f"Sequence Length: {config['model']['sequence_length']}\n")

        # 添加SNR范围信息
        if dataset_type in config['data'].get('snr_ranges', {}):
            snr_min, snr_max = config['data']['snr_ranges'][dataset_type]
            f.write(f"SNR Range: {snr_min} to {snr_max} dB\n")

        f.write(f"Overall Test Accuracy: {test_acc:.4f}%\n")
        f.write(f"Best Validation Accuracy: {best_val_acc:.4f}% (Epoch {best_epoch})\n")
        f.write(f"Total Epochs: {len(history['epochs'])}\n")

    logger.info(f"测试完成! 结果已保存到 {directories['experiment']}")

if __name__ == '__main__':
    main() 