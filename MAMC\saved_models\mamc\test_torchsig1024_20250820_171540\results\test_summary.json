{"overall_metrics": {"accuracy": 51.61634615384615, "macro_f1": 50.104121537224486, "kappa": 0.49600360576923075}, "model_complexity": {"macs": "4.117M", "parameters": "415.257K", "macs_raw": 4117120.0, "params_raw": 415257.0}, "inference_performance": {"avg_inference_time_ms": 0.046957869942371666, "std_inference_time_ms": 0.018420936308162515, "min_inference_time_ms": 0.018982216715812683, "max_inference_time_ms": 0.650063157081604}, "dataset_info": {"total_samples": 208000, "dataset_type": "torchsig1024", "input_shape": [2, 1024], "num_classes": 25, "snr_range": [0.0, 30.0]}, "test_info": {"model_path": "saved_models/mamc/torchsig1024_20250820_113738/models/best_model.pth", "config_path": "config.yaml", "test_date": "2025-08-20 17:17:07"}}