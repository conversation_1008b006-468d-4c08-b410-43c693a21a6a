dataset_info:
  dataset_type: torchsig2048
  input_shape: !!python/tuple
  - 2
  - 2048
  num_classes: 25
  snr_range:
  - 0.0
  - 30.0
  total_samples: 208000
inference_performance:
  avg_inference_time_ms: 0.0843633172603754
  max_inference_time_ms: 0.5989521741867065
  min_inference_time_ms: 0.07575377821922302
  std_inference_time_ms: 0.014663628303183426
model_complexity:
  macs: 592.185M
  macs_raw: 592185152.0
  parameters: 1.976M
  params_raw: 1976409.0
overall_metrics:
  accuracy: 62.98605769230769
  kappa: 0.6144381009615385
  macro_f1: 62.342787690939225
test_info:
  config_path: config.yaml
  model_path: ./saved_models/wnn_mrnn/torchsig2048_20250819_191959/models/best_model.pth
  test_date: '2025-08-20 11:15:46'
