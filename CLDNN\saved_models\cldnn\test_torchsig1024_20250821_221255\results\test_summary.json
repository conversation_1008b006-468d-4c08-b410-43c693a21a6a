{"overall_metrics": {"accuracy": 58.315865384615385, "macro_f1": 57.432577669075755, "kappa": 0.565790264423077}, "model_complexity": {"macs": "1.123G", "parameters": "950.489K", "macs_raw": 1122599296.0, "params_raw": 950489.0}, "inference_performance": {"avg_inference_time_ms": 0.09235036602387062, "std_inference_time_ms": 0.008123525432982025, "min_inference_time_ms": 0.0906381756067276, "max_inference_time_ms": 0.4168972373008728}, "dataset_info": {"total_samples": 208000, "dataset_type": "torchsig1024", "input_shape": [2, 1024], "num_classes": 25, "snr_range": [0.0, 30.0]}, "test_info": {"model_path": "./saved_models/cldnn/torchsig1024_20250709_104433/models/best_model.pth", "config_path": "config.yaml", "test_date": "2025-08-21 22:14:22"}}