{"overall_metrics": {"accuracy": 55.06923076923077, "macro_f1": 53.90982611822256, "kappa": 0.5319711538461538}, "model_complexity": {"macs": "8.230M", "parameters": "824.857K", "macs_raw": 8229504.0, "params_raw": 824857.0}, "inference_performance": {"avg_inference_time_ms": 0.05076897717439212, "std_inference_time_ms": 0.021078191805818217, "min_inference_time_ms": 0.020645558834075928, "max_inference_time_ms": 0.6950385868549347}, "dataset_info": {"total_samples": 208000, "dataset_type": "torchsig2048", "input_shape": [2, 2048], "num_classes": 25, "snr_range": [0.0, 30.0]}, "test_info": {"model_path": "saved_models/mamc/torchsig2048_20250820_172510/models/best_model.pth", "config_path": "config.yaml", "test_date": "2025-08-20 19:45:02"}}